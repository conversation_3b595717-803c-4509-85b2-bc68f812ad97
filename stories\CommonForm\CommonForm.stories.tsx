import type { StoryObj } from "@storybook/react";
import type { Meta } from "@storybook/react";
import { Form } from "./Form";
import "./index.css";
const meta: Meta<typeof Form> = {
  title: "CommonForm",
  tags: ["autodocs"],
  component: Form,
  argTypes: {},
};

export default meta;
type Story = StoryObj<typeof Form>;

enum YESNO {
  YES = 1,
  NO = 0,
}

const YES_NO_OPTIONS = [
  {
    label: "是",
    value: YESNO.YES,
  },
  {
    label: "否",
    value: YESNO.NO,
  },
];

const BugStatus = [
  {
    label: "待确认",
    value: "AWAIT_PROCESSING",
  },
  {
    label: "处理中",
    value: "PROCESSING",
  },
  {
    label: "已解决",
    value: "RESOLVED",
  },
  {
    label: "关闭",
    value: "CLOSE",
  },
  {
    label: "重新开启",
    value: "AGAIN_OPEN",
  },
  {
    label: "挂起",
    value: "HANG_UP",
  },
  {
    label: "已驳回",
    value: "REJECTED",
  },
  {
    label: "取消",
    value: "CANCEL",
  },
];

const InvalidReason = [
  {
    label: "数据收集",
    value: "DATA_COLLECTION",
  },
  {
    label: "重复缺陷",
    value: "REPEAT_DEFECT",
  },
  {
    label: "驳回缺陷",
    value: "REJECTION_DEFECT",
  },
];

const TestResultOptions = [
  {
    label: "待测试",
    value: 3,
  },
  {
    label: "长期测试",
    value: 2,
  },
  {
    label: "测试通过",
    value: 0,
  },
  {
    label: "测试不通过(保留)",
    value: 1,
  },
  {
    label: "测试不通过(已回滚)",
    value: 7,
  },
];

export const DarkTheme: Story = {
  args: {
    layout: "inline",
    theme: "dark",
    formType:'search',
    formConfig: {
      fields: [
        {
          fieldName: "deviceName",
          label: "车牌号(设备别名)",
          type: "input",
          placeholder: "请输入",
          labelCol: { span: 12 },
          xl: { span: 4 },
          xxl: { span: 4 },
        },
        {
          fieldName: "workMode",
          label: "模式",
          type: "select",
          placeholder: "请选择",
          labelCol: { span: 8 },
          wrapperCol: { span: 16 },
          xl: { span: 4 },
          xxl: { span: 4 },
        },
        {
          fieldName: "rangeTime",
          label: "起始时间",
          type: "rangeTime",
          placeholder: "请选择",
          labelCol: { span: 4 },
          wrapperCol: { span: 20 },
          xl: { span: 8 },
          xxl: { span: 8 },
        },
        {
          fieldName: "processMode",
          label: "是否现场处置",
          type: "select",
          placeholder: "请选择",
          options: [
            {
              label: "是",
              value: "LOCAL_PROCESS",
            },
            {
              label: "否",
              value: "REMOTE_PROCESS",
            },
          ],
        },
        {
          fieldName: "followUser",
          label: "处置人员",
          type: "input",
          placeholder: "请输入",
          labelCol: { span: 8 },
          wrapperCol: { span: 16 },
          xl: { span: 4 },
          xxl: { span: 4 },
        },
        {
          fieldName: "followUser",
          label: "仓名称",
          type: "input",
          placeholder: "请输入",
          labelCol: { span: 12 },
          xl: { span: 4 },
          xxl: { span: 4 },
        },
      ],
    },
  },
};
