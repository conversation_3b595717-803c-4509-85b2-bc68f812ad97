import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { TagList } from "./TagList";
import React from "react";

export default {
  title: "标签",
  component: TagList,
  tags: ["autodocs"],
  parameters: {
    layout: "fullscreen",
  },
};

type Story = StoryObj<typeof TagList>;
export const CustomFooter: Story = {
  render: (args) => <TagList />,
  args: {
    footer: "Built with Storybook",
  },
};
