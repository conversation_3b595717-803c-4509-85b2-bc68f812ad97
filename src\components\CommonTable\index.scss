@import "../../styles/resetAnt";
.x-coreui-common-table {
  background-color: white;
  margin-top: 10px;
  .enable {
    color: green;
  }
  .unenable {
    color: red;
  }
  .middle-btn {
    margin-bottom: 10px;
    // height: 32px;
    display: flex;
    align-items: center;
    Button {
      margin-right: 10px;
    }

    .column-setting-btn {
      margin-left: auto;
    }
  }

  // 列配置加载指示器
  .column-settings-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
  }

  // 列设置下拉菜单样式
  .column-setting-dropdown {
    padding: 12px;
    min-width: 320px;

    .column-setting-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;

      span {
        font-weight: 500;
      }
    }

    .column-setting-action-rest-button {
      color: #1890ff;
      cursor: pointer;
    }

    .column-setting-list {
      display: flex;
      flex-direction: column;
      width: 100%;

      &.column-setting-list-group {
        padding-top: 0;
      }

      &-title {
        margin-top: 6px;
        margin-bottom: 6px;
        padding-left: 24px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 12px;
      }

      &-item {
        display: flex;
        align-items: center;
        max-height: 24px;
        justify-content: space-between;

        &-title {
          flex: 1;
          max-width: 180px;
          text-overflow: ellipsis;
          overflow: hidden;
          word-break: break-all;
          white-space: nowrap;
        }

        &-option {
          display: none;
          float: right;
          cursor: pointer;

          > span {
            > span.anticon {
              color: #1890ff;
            }
          }

          > span + span {
            margin-left: 4px;
          }
        }

        &:hover {
          .column-setting-list-item-option {
            display: block;
          }
        }
      }
    }
  }

  // 树形组件样式
  .ant-tree-draggable-icon {
    cursor: grab;
  }

  .ant-tree-treenode {
    align-items: center;

    .ant-tree-checkbox {
      margin-right: 4px;
    }

    .ant-tree-title {
      width: 100%;
    }
  }

  a {
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: normal;
    color: rgba(60, 110, 240, 1);
    margin-right: 12px;
  }

  .#{$ant-prefix}-pagination {
    a {
      margin-right: 0px;
    }
  }
}
.dark-theme {
  /*table重置*/

  .#{$ant-prefix}-table-container {
    border-top: 0 !important;
  }

  .#{$ant-prefix}-table {
    font-size: $sizeNormal;
    color: $themeFontColor;
    background: transparent;
    border: 0 !important;

    &-filter-trigger {
      font-size: $sizeNormal;
      color: $themeFontColor;

      &.active {
        color: #0098ff;
      }
    }

    &-filter-trigger-container {
      bottom: -1px;
    }

    tr.#{$ant-prefix}-table-expanded-row,
    tr.#{$ant-prefix}-table-expanded-row:hover {
      background: $bgTableHeader;
    }

    &-filter-trigger-container-open,
    &-filter-trigger-container:hover,
    &-thead th.#{$ant-prefix}-table-column-has-sorters:hover .#{$ant-prefix}-table-filter-trigger-container,
    &-thead th.#{$ant-prefix}-table-column-has-sorters:hover .#{$ant-prefix}-table-filter-trigger-container:hover {
      background: $bgTableHeader;
    }

    &-filter-trigger-container-open .#{$ant-prefix}-table-filter-trigger,
    &-filter-trigger:hover {
      color: $themeFontColor;
    }

    &-filter-dropdown-btns {
      border-top: none;
    }

    &-filter-dropdown {
      background: linear-gradient(180deg, #3b3f54 0%, #3b3f54 100%);
    }

    .#{$ant-prefix}-table-header > table {
      border-top: 0 !important;
    }

    &-thead {
      background: $bgTableHeader !important;

      tr {
        th {
          color: $themeFontColor !important;
          background: $bgTableHeader !important;
          border: 0 !important;
          padding: 13px 16px;
        }

        .#{$ant-prefix}-table-column-sort {
          background: $bgDark !important;
        }

        .#{$ant-prefix}-table-column-has-sorters:hover {
          background: $bgDark;
        }
      }
    }

    &-tbody {
      tr {
        &:nth-child(even) > td {
          background: $bgTableEven;
        }

        &:nth-child(odd) > td {
          background: $bgTableOdd;
        }

        td {
          color: $themeFontColor !important;
          padding: 13px 16px !important;
          border-bottom: 1px solid rgba(10, 13, 26, 0.4) !important;
        }

        &.#{$ant-prefix}-table-row:hover {
          td {
            background: #3a3f57 !important;
          }
        }

        td.#{$ant-prefix}-table-column-sort {
          background: $bgDark !important;
        }
      }
    }

    tr > th,
    tr > td,
    .#{$ant-prefix}-table-container,
    .#{$ant-prefix}-table-cell-fix-right-first::after {
      border-right: 0 !important;
      border-left: 0 !important;
    }

    .#{$ant-prefix}-table-cell-scrollbar {
      box-shadow: none;
    }

    tr.#{$ant-prefix}-table-measure-row {
      visibility: collapse;
    }

    tr.#{$ant-prefix}-table-expanded-row {
      .#{$ant-prefix}-table-cell {
        background: #13172e !important;
      }
    }
    tr > th.#{$ant-prefix}-table-cell {
      &::before {
        display: none;
      }
    }
    .#{$ant-prefix}-table-summary {
      background: #292d45;
      & > tr > td {
        border: none;
      }
    }
  }

  .#{$ant-prefix}-table-wrapper .#{$ant-prefix}-table-tbody > tr.#{$ant-prefix}-table-placeholder:hover > td {
    background: #3a3f57;
  }

  /* 自定义 Ant Design Table 滚动条样式 */
  .#{$ant-prefix}-table-body::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .#{$ant-prefix}-table-body::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 8px;
  }

  .#{$ant-prefix}-table-body::-webkit-scrollbar-thumb {
    background: $bgTableOdd;
    border-radius: 10px;
  }

  .#{$ant-prefix}-table-body::-webkit-scrollbar-thumb:hover {
    background: $bgTableOdd;
  }

  /* Firefox 支持 */
  .#{$ant-prefix}-table-body {
    scrollbar-width: thin;
    scrollbar-color: $bgTableOdd $bgTableOdd;
  }
  .#{$ant-prefix}-table-container table {
    background-color: $bgTableOdd;
    border-right: none;
  }
}
