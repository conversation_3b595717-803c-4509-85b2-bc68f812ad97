import dayjs, { Dayjs } from "dayjs";

export const formatWeekStartDate = (week: number) => {
  return dayjs()?.week(week)?.startOf("week")?.format("MM/DD");
};

export const formatWeekEndofDate = (week: number) => {
  return dayjs()?.week(week)?.endOf("week")?.format("MM/DD");
};

export const isNullObject = (obj: any) => {
  return (
    Object.prototype.toString.call(obj) === "[object Object]" &&
    Object.keys(obj).length === 0
  );
};

export const filterOption = (
  input: string,
  option?: { label: string; value: string }
) => (option?.label ?? "").toLowerCase().includes(input.toLowerCase());

export const isNumber = (value: any) => {
  return typeof value === "number" && !isNaN(value);
};

export const deepClone = (source: any) => {
  if (typeof source !== "object") {
    return source;
  }
  const temp = Array.isArray(source) ? [] : {};
  for (const [key, value] of Object.entries(source)) {
    temp[key] = deepClone(value);
  }

  return temp;
};
