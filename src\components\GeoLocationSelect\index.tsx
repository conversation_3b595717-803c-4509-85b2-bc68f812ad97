import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Select, Tabs, ConfigProvider, Empty, Spin } from 'antd';
import { debounce } from 'lodash';
import { buildURL } from '../../fetch/util';
import './index.scss';
import $ from "jquery";
export interface DockPoint {
  id: string;
  name: string;
  address?: string;
  [key: string]: any;
}

export interface LocationPoint {
  id: string;
  title: string;
  address: string;
  location?: {
    lat: number;
    lng: number;
  };
  [key: string]: any;
}

export interface GeoLocationSelectProps {
  /**
   * 停靠点数据
   */
  dockPoints: DockPoint[];

  /**
   * 默认选中的Tab，默认为"dockPoint"
   */
  defaultActiveTab?: 'dockPoint' | 'location';

  /**
   * 腾讯地图配置（可选，如果不提供则使用默认配置）
   */
  tencentMapConfig?: {
    key?: string;  // 腾讯地图API密钥，默认使用fetchPoiList中的密钥
    debounceTime?: number;  // 搜索防抖时间，默认300ms
  };

  /**
   * 选中项变化回调
   */
  onChange?: (value: any, option: any) => void;

  /**
   * 搜索框值变化回调
   */
  onSearch?: (value: string) => void;

  /**
   * 自定义渲染选项
   */
  optionRender?: (item: any, tabType: 'dockPoint' | 'location') => React.ReactNode;

  /**
   * 占位符文本
   */
  placeholder?: string;

  /**
   * 是否禁用
   */
  disabled?: boolean;

  /**
   * 样式类名
   */
  className?: string;

  /**
   * 样式对象
   */
  style?: React.CSSProperties;

  /**
   * 是否允许清除
   */
  allowClear?: boolean;

  /**
   * 当前选中的值
   */
  value?: any;

  /**
   * 默认选中的值
   */
  defaultValue?: any;
}

const { TabPane } = Tabs;

/**
 * 地理位置下拉搜索框组件
 */
const GeoLocationSelect: React.FC<GeoLocationSelectProps> = (props) => {
  const {
    dockPoints = [],
    defaultActiveTab = 'dockPoint',
    tencentMapConfig = {},
    onChange,
    onSearch,
    optionRender,
    placeholder = '请输入地点名称搜索',
    disabled = false,
    className = '',
    style = {},
    allowClear = true,
    value,
    defaultValue,
  } = props;

  // 状态
  const [activeTab, setActiveTab] = useState<'dockPoint' | 'location'>(defaultActiveTab);
  const [searchText, setSearchText] = useState<string>('');
  const [locationOptions, setLocationOptions] = useState<LocationPoint[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [dropdownVisible, setDropdownVisible] = useState<boolean>(false);
  const [selectedValue, setSelectedValue] = useState<any>(defaultValue);

  // 防抖搜索
  const debouncedSearch = useRef(
    debounce((value: string) => {
      if (activeTab === 'location' && value) {
        fetchLocationData(value);
      }
      onSearch && onSearch(value);
    }, tencentMapConfig.debounceTime || 300)
  ).current;

  // 过滤后的停靠点数据
  const filteredDockPoints = useMemo(() => {
    if (!searchText) return dockPoints;

    return dockPoints.filter(item =>
      item.name.includes(searchText) ||
      (item.address && item.address.includes(searchText))
    );
  }, [dockPoints, searchText]);

  // 获取位置数据
  const fetchLocationData = async (keyword: string) => {
    if (!keyword) {
      setLocationOptions([]);
      return;
    }

    setLoading(true);
    try {
      const results = await fetchPoiList(keyword);
      // 转换数据格式
      const formattedResults = results.map((item: any) => ({
        id: item.id,
        title: item.title,
        address: item.address || '',
        location: item.location || null,
        ...item
      }));
      setLocationOptions(formattedResults);
    } catch (error) {
      console.error('搜索位置出错:', error);
      setLocationOptions([]);
    } finally {
      setLoading(false);
    }
  };

  // 腾讯地图POI搜索
  const fetchPoiList = (searchText: string): Promise<any[]> => {
    return new Promise((resolve, reject) => {
      $.ajax({
        type: 'get',
        url: buildURL({
          absoluteURL: 'https://apis.map.qq.com/ws/place/v1/suggestion',
          urlParams: {
            key: 'KRBBZ-VJEWS-Q3IOZ-67LGG-RVU2E-UEF6X',
            keyword: encodeURI(searchText),
            output: 'jsonp',
          },
        }),
        dataType: 'jsonp',
        success: (res: any) => {
          if (res.status == 0) {
            resolve(res?.data ?? []);
          } else {
            reject(new Error(res.message || '搜索失败'));
          }
        },
        error: (err: any) => {
          reject(err);
        }
      });
    });
  };

  // 处理搜索输入
  const handleSearch = (value: string) => {
    setSearchText(value);
    debouncedSearch(value);
  };

  // 处理选择变化
  const handleChange = (val: any, option: any) => {
    // 由于使用了labelInValue模式，value是一个对象，包含value和label属性
    setSelectedValue(val);
    onChange && onChange(val, option);
  };

  // 监听外部value变化
  useEffect(() => {
    if (value !== undefined) {
      setSelectedValue(value);
    }
  }, [value]);

  // 处理Tab切换
  const handleTabChange = (key: string) => {
    setActiveTab(key as 'dockPoint' | 'location');
    // 切换Tab时重新搜索
    if (key === 'location' && searchText) {
      fetchLocationData(searchText);
    }
  };

  // 自定义下拉菜单内容
  const dropdownRender = (menu: React.ReactElement) => {
    return (
      <div
        className="geo-location-dropdown"
        // 阻止整个下拉面板的点击事件冒泡
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <Tabs
          activeKey={activeTab}
          onChange={handleTabChange}
          className="geo-location-tabs"
          // 阻止Tab点击事件冒泡，防止下拉框关闭
          onTabClick={(_, e) => {
            e.stopPropagation();
          }}
        >
          <TabPane tab="停靠点" key="dockPoint">
            {loading ? (
              <div className="loading-container">
                <Spin tip="加载中..." />
              </div>
            ) : filteredDockPoints.length > 0 ? (
              menu
            ) : (
              <Empty description="暂无数据" />
            )}
          </TabPane>
          <TabPane tab="位置" key="location">
            {loading ? (
              <div className="loading-container">
                <Spin tip="加载中..." />
              </div>
            ) : locationOptions.length > 0 ? (
              menu
            ) : (
              <Empty description={searchText ? "未找到相关位置" : "请输入关键词搜索"} />
            )}
          </TabPane>
        </Tabs>
      </div>
    );
  };

  // 根据当前Tab获取选项数据
  const getOptions = () => {
    if (activeTab === 'dockPoint') {
      return filteredDockPoints.map(item => ({
        label: optionRender ?
          optionRender(item, 'dockPoint') :
          (
            <div className="option-item">
              <div className="option-name">{item.name}</div>
              {item.address && <div className="option-address">{item.address}</div>}
            </div>
          ),
        // 用于选中后显示的简洁标签
        customLabel: item.name,
        value: item.id,
        item
      }));
    } else {
      return locationOptions.map(item => ({
        label: optionRender ?
          optionRender(item, 'location') :
          (
            <div className="option-item">
              <div className="option-name">{item.title}</div>
              {item.address && <div className="option-address">{item.address}</div>}
            </div>
          ),
        // 用于选中后显示的简洁标签
        customLabel: item.title,
        value: item.id,
        item
      }));
    }
  };

  return (
    <ConfigProvider prefixCls="x-coreui">
      <div className={`geo-location-select ${className}`} style={style}>
        <Select
          showSearch
          placeholder={placeholder}
          optionFilterProp="children"
          onChange={handleChange}
          onSearch={handleSearch}
          onDropdownVisibleChange={(visible) => {
            setDropdownVisible(visible);
            // 当下拉框打开且当前是位置Tab时，如果有搜索文本则执行搜索
            if (visible && activeTab === 'location' && searchText) {
              fetchLocationData(searchText);
            }
          }}
          dropdownRender={dropdownRender}
          options={getOptions()}
          disabled={disabled}
          allowClear={allowClear}
          className="geo-select"
          filterOption={false}
          // 防止点击内容区域自动关闭下拉框
          popupMatchSelectWidth={false}
          getPopupContainer={(triggerNode) => triggerNode.parentNode}
          open={dropdownVisible}
          // 自定义选中项的显示内容
          labelInValue
          fieldNames={{
            label: 'label',
            value: 'value'
          }}
          optionLabelProp="customLabel"
          value={selectedValue}
        />
      </div>
    </ConfigProvider>
  );
};

export default GeoLocationSelect;
