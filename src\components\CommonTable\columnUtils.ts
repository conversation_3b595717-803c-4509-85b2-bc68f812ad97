import type { ColumnsType } from "antd/es/table";
import { ColumnState } from "./ColumnSetting";

/**
 * 生成列的唯一键
 * @param key 列的key
 * @param index 列的索引
 * @returns 列的唯一键
 */
export const genColumnKey = (key?: React.Key, index?: number) => {
  if (key) {
    return key.toString();
  }
  return `${index}`;
};

/**
 * 创建默认的列显示状态配置
 * @param columns 表格列配置
 * @param hiddenColumns 需要默认隐藏的列的key数组
 * @param leftFixedColumns 需要默认固定在左侧的列的key数组
 * @param rightFixedColumns 需要默认固定在右侧的列的key数组
 * @returns 列显示状态配置对象
 */
export const createDefaultColumnsState = (
  columns: ColumnsType<any>,
  hiddenColumns: string[] = [],
  leftFixedColumns: string[] = [],
  rightFixedColumns: string[] = []
): Record<string, ColumnState> => {
  const columnsState: Record<string, ColumnState> = {};

  columns.forEach((column, index) => {
    const key = genColumnKey(column.key || (column as any).dataIndex, index);
    if (key) {
      // 默认列状态
      const state: ColumnState = {
        show: !hiddenColumns.includes(key),
        order: index,
      };

      // 如果列已经有固定属性，使用列的固定属性
      if (column.fixed) {
        state.fixed = column.fixed === true ? "left" : column.fixed;
      }
      // 如果列在leftFixedColumns数组中，则固定在左侧
      else if (leftFixedColumns.includes(key)) {
        state.fixed = "left";
      }
      // 如果列在rightFixedColumns数组中，则固定在右侧
      else if (rightFixedColumns.includes(key)) {
        state.fixed = "right";
      }

      columnsState[key] = state;
    }
  });

  return columnsState;
};

/**
 * 根据列显示状态过滤和处理列
 * @param columns 原始列配置
 * @param columnsMap 列显示状态配置
 * @returns 过滤和处理后的列配置
 */
export const filterColumnsByState = (
  columns: ColumnsType<any>,
  columnsMap: Record<string, ColumnState>
): ColumnsType<any> => {
  // 先根据显示状态过滤
  const filteredColumns = columns.filter((column, index) => {
    const key = genColumnKey(column.key || (column as any).dataIndex, index);
    return !key || columnsMap[key]?.show !== false;
  });

  // 应用固定列设置
  return filteredColumns.map((column, index) => {
    const key = genColumnKey(column.key || (column as any).dataIndex, index);
    const config = columnsMap[key];
    if (config && config.fixed) {
      return { ...column, fixed: config.fixed };
    }
    return column;
  });
};

/**
 * 根据列状态对列进行排序
 * @param columns 原始列配置
 * @param columnsMap 列状态配置
 * @returns 排序后的列配置
 */
export const sortColumnsByState = (
  columns: ColumnsType<any>,
  columnsMap: Record<string, ColumnState>
): ColumnsType<any> => {
  // 创建一个包含原始列和对应key的数组
  const columnsWithKey = columns.map((column, index) => {
    const key = genColumnKey(column.key || (column as any).dataIndex, index);
    return { column, key };
  });

  // 根据列状态中的order属性进行排序
  return columnsWithKey
    .sort((a, b) => {
      const orderA = columnsMap[a.key]?.order ?? 0;
      const orderB = columnsMap[b.key]?.order ?? 0;
      return orderA - orderB;
    })
    .map(({ column }) => column);
};

// 默认导出所有工具函数
const columnUtils = {
  genColumnKey,
  createDefaultColumnsState,
  filterColumnsByState,
  sortColumnsByState,
};

export default columnUtils;
