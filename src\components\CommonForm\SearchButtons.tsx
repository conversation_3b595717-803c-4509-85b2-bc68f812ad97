import { But<PERSON>, <PERSON> } from "antd";
import React, { memo } from "react";

export const SearchButtons = memo(
  ({
    onReset,
    onSearch,
    resetBtnClstag,
    searchBtnClstag,
  }: {
    onReset: () => void;
    onSearch: () => void;
    resetBtnClstag?: string;
    searchBtnClstag?: string;
  }) => (
    <Row justify="end" className="searchbtns">
      <Button
        className="reset"
        data-qidian-cid="coreui_commonForm"
        clstag={resetBtnClstag ?? "searchForm_resetBtn"}
        style={{ marginRight: "12px" }}
        onClick={onReset}
      >
        重置
      </Button>
      <Button
        type="primary"
        data-qidian-cid="coreui_commonForm"
        clstag={searchBtnClstag ?? "searchForm_searchBtn"}
        onClick={onSearch}
      >
        查询
      </Button>
    </Row>
  )
);
