import React from 'react';
import { AnyFunc } from '../../../global';

export interface WeekHeaderProps {
  currentDate: any;
  changeDate: AnyFunc;
}

const WeekHeader: React.FC<WeekHeaderProps> = (props) => {
  const { currentDate, changeDate } = props;
  return (
    <div className="week-header">
      <span
        className="pre-btn"
        onClick={() => {
          const newYear = currentDate.year() - 1;
          changeDate(currentDate.year(newYear));
        }}
      >
        {'<<'}
      </span>
      <span
        className="pre-btn"
        onClick={() => {
          const newYear = currentDate.month() - 1;
          changeDate(currentDate.month(newYear));
        }}
      >
        {'<'}
      </span>
      <span className="date-regin">
        {currentDate.year()}年 {currentDate.month() + 1} 月
      </span>
      <span
        className="next-btn"
        onClick={() => {
          const newYear = currentDate.month() + 1;
          changeDate(currentDate.month(newYear));
        }}
      >
        {'>'}
      </span>
      <span
        className="next-btn"
        onClick={() => {
          const newYear = currentDate.year() + 1;
          changeDate(currentDate.year(newYear));
        }}
      >
        {'>>'}
      </span>
    </div>
  );
};

export default WeekHeader;
