import { ColumnConfig, ValidationResult, ValidatorFunction } from './types';

export class ValidatorManager {
  private validators: Record<string, ValidatorFunction> = {};

  constructor() {
    this.registerDefaultValidators();
  }

  private registerDefaultValidators() {
    // 注册默认校验器
    this.validators['required'] = (value: any, config: ColumnConfig): ValidationResult => ({
      valid: value !== undefined && value !== null && value !== '',
      errors: ['该字段为必填项']
    });

    this.validators['number'] = (value: any): ValidationResult => ({
      valid: !isNaN(Number(value)),
      errors: ['该字段必须为数字']
    });

    this.validators['date'] = (value: any): ValidationResult => ({
      valid: !isNaN(Date.parse(value)),
      errors: ['该字段必须为有效日期']
    });

    this.validators['enum'] = (value: any, config: ColumnConfig): ValidationResult => ({
      valid: config.options?.includes(value) ?? false,
      errors: [`该字段必须是以下值之一: ${config.options?.join(', ')}`]
    });
  }

  registerValidator(name: string, validator: ValidatorFunction) {
    this.validators[name] = validator;
  }

  validate(value: any, config: ColumnConfig): ValidationResult {
    const errors: string[] = [];
    let isValid = true;

    // 检查必填
    if (config.required) {
      const requiredResult = this.validators['required'](value, config);
      if (!requiredResult.valid) {
        errors.push(...requiredResult.errors);
        isValid = false;
      }
    }

    // 如果值为空且不是必填，则跳过其他校验
    if (value === undefined || value === null || value === '') {
      return { valid: isValid, errors };
    }

    // 类型校验
    if (config.type && this.validators[config.type]) {
      const typeResult = this.validators[config.type](value, config);
      if (!typeResult.valid) {
        errors.push(...typeResult.errors);
        isValid = false;
      }
    }

    // 自定义校验
    if (config.validator && this.validators[config.validator]) {
      const customResult = this.validators[config.validator](value, config);
      if (!customResult.valid) {
        errors.push(...customResult.errors);
        isValid = false;
      }
    }

    return { valid: isValid, errors };
  }
}
