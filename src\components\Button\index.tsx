import React, { ReactNode } from "react";
import "./index.scss";
import classNames from "classnames";
export interface ButtonProps {
  classNames?: string;
  onClick: (e: Element) => void;
  children?: ReactNode;
}
const Button = (props: ButtonProps) => {
  const handleMouseDown = (
    e: React.MouseEvent<HTMLButtonElement, MouseEvent>
  ) => {
    console.log(e.target);
  };
  const handleMouseUp = (
    e: React.MouseEvent<HTMLButtonElement, MouseEvent>
  ) => {};
  return (
    <button
      className={classNames("base-btn", props.classNames)}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      onClick={(e: any) => {
        props.onClick && props.onClick(e);
      }}
    >
      {props.children}
      <i className="ripple"></i>
    </button>
  );
};
export default Button;
