import React, { useEffect, useRef, useState } from "react";
import { buildURL } from "../../fetch/util";
import { getCookie } from "./util";
import $ from "jquery";
import "./index.scss";
import Button from "@components/Button";

export interface PassportLoginProps {
  returnUrl: string;
}
const PassportLogin = (props: PassportLoginProps) => {
  const timeCode = useRef<any>(null);
  const [qrCodeInfo, setQrCodeInfo] = useState<any>(null);
  const [qrCodeTime, setQrCodeTime] = useState<any>(Date.now());
  /*
 定时轮询二维码是否被app扫描
*/
  const checkQrcode = () => {
    const url = buildURL({
      absoluteURL: `https://qr.jdl.cn/check`,
      urlParams: {
        appid: "1673",
        callback: "handleCallback",
        token: getCookie("wlfstk_smdl"),
        _: new Date().getTime(),
      },
    });
    $.ajax({
      url: url,
      dataType: "jsonp",
      headers: {
        QRCodeKey: getCookie("QRCodeKey"),
      },
    });
  };

  const handleRefresh = () => {
    setQrCodeTime(new Date().getTime());
  };

  const passportLogin = () => {
    const loginUrl = `https://sso.jdl.cn/sso/login?ReturnUrl=${props.returnUrl}`;
    document.location.href = loginUrl;
  };
  useEffect(() => {
    /*
  轮询二维码回调函数
  二维码状态：
  code: 200 授权登录
  code: 201 "二维码未扫描，请扫描二维码"
  code: 202 "请手机客户端确认登录"
  code: 203 '二维码无效'
  code: 205  '二维码已取消授权'
*/
    (window as any).handleCallback = (resp: any) => {
      setQrCodeInfo(resp);
      if (resp.code == 200) {
        clearInterval(timeCode.current);
        timeCode.current = null;

        //  请求passport写登录态cookie的地址
        const url = "https://ssa.jdl.cn/sso/redirect";
        const baseURL = props.returnUrl?.startsWith("http")
          ? props.returnUrl
          : location.protocol + "//" + props.returnUrl;
        const encodeUrl = encodeURIComponent(encodeURIComponent(baseURL));
        document.cookie = `BakReturnUrl=${encodeUrl}; path=/; domain=.jdl.cn`;
        const buildPassportUrl = buildURL({
          absoluteURL:
            "https://passport.jd.com/uc/qrCodeTicketValidation?sso=1&source=jdxmonitor_pc",
          urlParams: {
            t: resp.ticket,
            ReturnUrl: url,
            callback: "callback",
          },
        });
        $.ajax({
          url: buildPassportUrl,
          dataType: "jsonp",
          header: {
            QRCodeKey: getCookie("QRCodeKey"),
          },
        });
      }
    };
    /**
     * setCookie 将thor从jd.com写到jdl.cn
     * @param {any} resp
     */
    (window as any).callback = (resp: any) => {
      localStorage.setItem("reload", "true");
      window.location.href = resp.url;
    };
  }, []);

  useEffect(() => {
    timeCode.current = setInterval(checkQrcode, 3000);
    return () => {
      clearInterval(timeCode.current);
      timeCode.current = null;
    };
  }, [qrCodeTime]);
  return (
    <div className="passport-box">
      <div className="qrCode-content">
        {(qrCodeInfo?.code == 203 ||
          qrCodeInfo?.code === 257 ||
          qrCodeInfo?.code === 205) && (
          <div className="qrCode-error-mask">
            <div className="qrCode-error-msg">二维码无效</div>
            <div className="refresh" onClick={handleRefresh}>
              刷新
            </div>
          </div>
        )}
        {qrCodeInfo?.code === 202 && (
          <div className="qrCode-success-mask">
            <img src="https://misc.360buyimg.com/user/passport/1.0.0/css/i/qrcode-succ-2016.png"></img>
            <div>请手机客户端确认登录</div>
          </div>
        )}
        {qrCodeTime && (
          <img
            className="qrCode-img"
            src={`https://qr.jdl.cn/show?appid=1673&size=160&t=${qrCodeTime}`}
          ></img>
        )}
      </div>
      <div className="notice-title">打开手机京东 扫描二维码</div>
      <Button
        classNames="erp_buttons"
        onClick={() => {
          document.cookie =
            "ReturnUrl=0;domain=.jdl.cn;expires=" +
            new Date(0).toUTCString() +
            ";path=/";
          passportLogin();
        }}
      >
        京东账号登录
      </Button>
    </div>
  );
};

export default PassportLogin;
