@import '../DatePickerGroup/variable';
.dark{
  .date-picker-container {
    width: 330px;
    position: relative;
    .date-select-item {
      font-size: 14px;
      font-family: PingFang SC;
      color: $whiteFontColor;
      display: flex;
      align-items: center;
      font-size: 14px;
      background: #262B40;
      border-radius: 1px;
      margin-left: 4px;
      padding: 0 4px;
      min-width: 42px;
      max-width: 64px;
      word-break: keep-all;
      .date-select-item_remove {
        margin-left: 4px;
        cursor: pointer;
      }
    }
  
    .date-label {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      padding: 6px 0px;
      background: $darkInputBg;
      border: 1px solid $borderColor;
      border-radius: 2px;
      display: flex;
      align-items: center;
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: normal;
      color: $darkInputPlaceholderFontColor;
  
      .date-label_content {
        border: none;
        outline: none;
        display: flex;
        max-width: 90%;
        flex: 1;
        background-color: transparent;
  
        .date-content_input {
          background-color: transparent;
          color: rgba(255, 255, 255, 0.6);
          font-size: 14px;
          font-family: PingFang SC;
          font-weight: normal;
        }
      }
  
      .icon {
        margin-left: 10px;
        line-height: 12px;
        margin-right: 4px;
        svg {
          color: #d9d9d9;
          width: 18px;
          height: 18px;
        }
      }
  
      .clear {
        color: #5B5B5E;
        cursor: pointer;
        width: 14px;
        height: 14px;
        line-height: 14px;
        text-align: center;
        position: absolute;
        right: 8px;
      }
    }
  
    .date-panel-container {
      position: absolute;
      top: 40px;
      left: 0;
      min-width: 340px;
      min-height: 170px;
      z-index: 9;
      background: rgba(27, 32, 56, 1);
      border-radius: 3px;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.5);
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: normal;
      color: #CFCFD5;
  
      .month-header,
      .week-header {
        text-align: center;
        color: #0D85FF;
        font-size: 18px;
        font-family: Helvetica;
        font-weight: bold;
        margin-top: 12px;
  
        .date-regin {
          margin-left: 24px;
          margin-right: 24px;
        }
  
        .pre-btn,
        .next-btn {
          display: inline-block;
          vertical-align: middle;
          width: 24px;
          height: 24px;
          cursor: pointer;
          text-align: center;
          line-height: 24px;
          color: #0D85FF;
          font-size: 18px;
          font-family: Helvetica;
        }
      }
  
      .panel-modal {
        height: 150px;
  
        .date-cell-list {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          height: 100%;
          padding: 0 10px;
          .cell-item {
            flex: 0 0 25%;
            text-align: center;
            cursor: pointer;
            .cell-item-text {
              width: 80%;
              box-sizing: border-box;
              padding: 4px;
            }
  
            .cell-item-subtext {
              font-size: 10px;
              width: 80%;
            }
  
            &.cell-active {
              color: #0D85FF;
            }
  
            &.cell-selected {
              .cell-item-text,.cell-item-subtext {
                color: #fff;
                background: #0D85FF;
              }
            }
          }
        }
      }
  
    }
  }
}
