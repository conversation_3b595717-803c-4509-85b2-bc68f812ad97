import React, { useEffect, useRef, useState } from "react";
import dayjs, { Dayjs } from "dayjs";
import weekOfYear from "dayjs/plugin/weekOfYear";
import DateInput from "./DateInput";
import DatePanel from "./DatePanel";
import "./index.scss";
import { DateType } from "./type";
import { AnyFunc } from "../../global";
import { ConfigProvider } from "antd";
dayjs.extend(weekOfYear);
export interface DatePickerProps {
  type: DateType;
  maxSize: number;
  maxTagCount: number;
  defaultDate: Dayjs;
  onChange: AnyFunc;
}

const DatePicker: React.FC<DatePickerProps> = (props) => {
  const { type, maxSize, maxTagCount, defaultDate, onChange } = props;
  const [panelVisible, setPanelVisible] = useState<boolean>(false);
  const [currentDate, setCurrentDate] = useState<Dayjs>(defaultDate);
  const [selectedList, setSelectedList] = useState<any>([]);
  const pickerRef = useRef<HTMLDivElement>(null);
  const handleDomClick = (e: Event) => {
    if (
      !pickerRef.current?.contains(e.target as Node) &&
      pickerRef.current !== e.target &&
      panelVisible
    ) {
      setPanelVisible(false);
    }
  };
  useEffect(() => {
    window.addEventListener("click", handleDomClick);
    return () => {
      window.removeEventListener("click", handleDomClick);
    };
  }, [panelVisible]);

  useEffect(() => {
    setSelectedList([]);
    onChange([]);
  }, [type]);

  return (
    <ConfigProvider prefixCls="x-coreui">
      <div className="date-picker-container" ref={pickerRef}>
        <DateInput
          maxTagCount={maxTagCount}
          placeholder="请选择"
          selectedList={selectedList}
          onClick={() => {
            setPanelVisible(!panelVisible);
          }}
          onClear={() => {
            setSelectedList([]);
            onChange([]);
          }}
          onDelete={(value: string | number) => {
            const filterList = selectedList.filter(
              (item: { value: string | number; text: string }) =>
                item.value !== value
            );
            setSelectedList(filterList);
            onChange(filterList);
          }}
        />
        <DatePanel
          type={type}
          maxSize={maxSize}
          currentDate={currentDate}
          visible={panelVisible}
          defaultDate={defaultDate}
          selectedList={selectedList}
          changeDate={(date: Dayjs) => {
            setCurrentDate(date);
          }}
          changeSelect={(value: { value: string | number; text: string }[]) => {
            setSelectedList(value);
            onChange(value);
          }}
        />
      </div>
    </ConfigProvider>
  );
};

export default DatePicker;
