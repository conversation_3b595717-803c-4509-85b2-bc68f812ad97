{
  "compilerOptions": {
    "downlevelIteration": true,
    "declaration": true,
    "target": "es5",
    "lib": [
      "dom",
      "dom.iterable",
      "esnext"
    ],
    "paths": {
      "@components/*": [
        "./src/components/*"
      ],
      "@utils/*": [
        "./src/utils/*"
      ],
    },
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": false,
    "allowUnusedLabels": true,
    "jsx": "react",
    "noImplicitAny": false,
    "rootDir": "./src",
    "declarationDir": "./lib"
  },
  "include": [
    "./src/**/*",
  ],
}