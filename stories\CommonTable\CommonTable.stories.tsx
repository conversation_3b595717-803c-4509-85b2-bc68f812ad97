import type { Meta } from "@storybook/react";
import type { StoryObj } from "@storybook/react";
import { CommonTable } from "./Table";
import React, { useState } from "react";
import { createDefaultColumnsState, sortColumnsByState } from "../../src/components/CommonTable/columnUtils";
import type { ColumnsType } from "antd/es/table";
import { ColumnSettingServiceOptions } from "../../src/services/columnSettingService";

const meta: Meta<typeof CommonTable> = {
  title: "CommonTable",
  tags: ["autodocs"],
  component: CommonTable,
  argTypes: {},
};

export default meta;
type Story = StoryObj<typeof CommonTable>;

// 可配置列的示例
const ConfigurableColumnsTemplate = (args: any) => {
  // 列数据
  const columns: ColumnsType<any> = [
    {
      title: "序号",
      dataIndex: "order",
      align: "center" as const,
      width: 80,
      render: (_text: any, _record: any, index: number) => index + 1,
    },
    {
      title: "设备分组设备分组设备分组设备分组",
      dataIndex: "groupName",
      align: "center" as const,
      ellipsis: true,
    },
    {
      title: "型号",
      dataIndex: "productModelNo",
      align: "center" as const,
      ellipsis: true,
    },
    {
      title: "分组",
      dataIndex: "groupName2",
      align: "center" as const,
      ellipsis: true,
    },
    {
      title: "SN",
      dataIndex: "deviceName",
      align: "center" as const,
      ellipsis: true,
    },
    {
      title: "电量",
      dataIndex: "deviceRestElectric",
      align: "center" as const,
      ellipsis: true,
    },
    {
      title: "状态",
      dataIndex: "realtimeStatusName",
      align: "center" as const,
      ellipsis: true,
    },
    {
      title: "操作",
      dataIndex: "action",
      align: "center" as const,
      ellipsis: true,
    },
    {
      title: "操作2",
      dataIndex: "action2",
      align: "center" as const,
      ellipsis: true,
    },
    {
      title: "操作3",
      dataIndex: "action3",
      align: "center" as const,
      ellipsis: true,
    },
    {
      title: "操作4",
      dataIndex: "action4",
      align: "center" as const,
      ellipsis: true,
    },
    {
      title: "操作5",
      dataIndex: "action5",
      align: "center" as const,
      ellipsis: true,
    },
    {
      title: "操作6",
      dataIndex: "action6",
      align: "center" as const,
      ellipsis: true,
    },
    {
      title: "操作7",
      dataIndex: "action7",
      align: "center" as const,
      ellipsis: true,
    },
    {
      title: "操作8",
      dataIndex: "action8",
      align: "center" as const,
      ellipsis: true,
    }
  ];

  // 默认隐藏的列
  const defaultHiddenColumns = ["deviceRestElectric", "realtimeStatusName"];

  // 默认固定在左侧的列
  const defaultLeftFixedColumns = ["order"];

  // 默认固定在右侧的列
  const defaultRightFixedColumns = [];

  // 列显示状态
  const [columnsState, setColumnsState] = useState(
    createDefaultColumnsState(columns, defaultHiddenColumns, defaultLeftFixedColumns, defaultRightFixedColumns)
  );

  // 应用列排序
  const sortedColumns = sortColumnsByState(columns, columnsState);

  return (
    <CommonTable
      {...args}
      columns={sortedColumns}
      showColumnSetting={true}
      tableKey="storybook-demo-table"
      columnsState={{
        value: columnsState,
        onChange: setColumnsState,
        persistenceType: "localStorage",
      }}
      defaultColumnsState={createDefaultColumnsState(columns, defaultHiddenColumns, defaultLeftFixedColumns, defaultRightFixedColumns)}
    />
  );
};

export const ConfigurableColumnsLight = {
  render: ConfigurableColumnsTemplate,
  args: {
    tableListData: {
      list: [
        {
          deviceName: "TDEV-1011-41",
          groupName: "测试专用分组",
          groupName2: "设备分组A",
          productModelNo: "pm00003",
          realtimeStatus: "OFFLINE",
          realtimeStatusName: "离线",
          deviceRestElectric: "85%",
        },
        {
          deviceName: "TDEV-1011-39",
          groupName: "测试专用分组",
          groupName2: "设备分组B",
          productModelNo: "pm00003",
          realtimeStatus: "ONLINE",
          realtimeStatusName: "在线",
          deviceRestElectric: "72%",
        },
        {
          deviceName: "TDEV-1011-38",
          groupName: "测试专用分组",
          groupName2: "设备分组C",
          productModelNo: "pm00004",
          realtimeStatus: "ONLINE",
          realtimeStatusName: "在线",
          deviceRestElectric: "63%",
        },
      ],
      totalNumber: 3,
      totalPage: 1,
    },
    loading: false,
    rowKey: "deviceName",
  },
  parameters: {
    docs: {
      description: {
        story: '这个示例展示了浅色主题下的可配置列功能。点击右上角的设置图标可以配置显示/隐藏列、固定列和拖拽排序。默认隐藏了“电量”和“状态”列，序号列固定在左侧。',
      },
    },
  },
};

export const ConfigurableColumnsDark = {
  render: ConfigurableColumnsTemplate,
  args: {
    theme: "dark",
    tableListData: {
      list: [
        {
          deviceName: "TDEV-1011-41",
          groupName: "测试专用分组",
          groupName2: "设备分组A",
          productModelNo: "pm00003",
          realtimeStatus: "OFFLINE",
          realtimeStatusName: "离线",
          deviceRestElectric: "85%",
        },
        {
          deviceName: "TDEV-1011-39",
          groupName: "测试专用分组",
          groupName2: "设备分组B",
          productModelNo: "pm00003",
          realtimeStatus: "ONLINE",
          realtimeStatusName: "在线",
          deviceRestElectric: "72%",
        },
        {
          deviceName: "TDEV-1011-38",
          groupName: "测试专用分组",
          groupName2: "设备分组C",
          productModelNo: "pm00004",
          realtimeStatus: "ONLINE",
          realtimeStatusName: "在线",
          deviceRestElectric: "63%",
        },
      ],
      totalNumber: 3,
      totalPage: 1,
    },
    loading: false,
    rowKey: "deviceName",
  },
  parameters: {
    docs: {
      description: {
        story: '这个示例展示了暗色主题下的可配置列功能。点击右上角的设置图标可以配置显示/隐藏列、固定列和拖拽排序。默认隐藏了“电量”和“状态”列，序号列固定在左侧。',
      },
    },
  },
};

// API 同步功能的示例
const ApiSyncColumnsTemplate = (args: any) => {
  // 列数据
  const columns: ColumnsType<any> = [
    {
      title: "序号",
      dataIndex: "order",
      align: "center" as const,
      width: 80,
      render: (_text: any, _record: any, index: number) => index + 1,
    },
    {
      title: "设备分组",
      dataIndex: "groupName",
      align: "center" as const,
      ellipsis: true,
    },
    {
      title: "型号",
      dataIndex: "productModelNo",
      align: "center" as const,
      ellipsis: true,
    },
    {
      title: "分组",
      dataIndex: "groupName2",
      align: "center" as const,
      ellipsis: true,
    },
    {
      title: "SN",
      dataIndex: "deviceName",
      align: "center" as const,
      ellipsis: true,
    },
    {
      title: "电量",
      dataIndex: "deviceRestElectric",
      align: "center" as const,
      ellipsis: true,
    },
    {
      title: "状态",
      dataIndex: "realtimeStatusName",
      align: "center" as const,
      ellipsis: true,
    },
  ];

  // 默认隐藏的列
  const defaultHiddenColumns = ["deviceRestElectric", "realtimeStatusName"];

  // 默认固定在左侧的列
  const defaultLeftFixedColumns = ["order"];

  // 默认固定在右侧的列
  const defaultRightFixedColumns = [];

  // 列显示状态
  const [columnsState, setColumnsMap] = useState(
    createDefaultColumnsState(columns, defaultHiddenColumns, defaultLeftFixedColumns, defaultRightFixedColumns)
  );

  // 应用列排序
  const sortedColumns = sortColumnsByState(columns, columnsState);

  // API 配置
  const apiConfig: ColumnSettingServiceOptions = {
    fetchPath: "/api/column-settings/query",
    savePath: "/api/column-settings/save",
    useAbsoluteURL: false,
    LOPDN: "demo-lopdn-value",
    timeout: 30000
  };

  return (
    <div>
      <div style={{ marginBottom: "16px" }}>
        <p><strong>注意：</strong> 这个示例中的 API 调用是模拟的，实际使用时需要配置真实的 API 端点。</p>
        <p>API 配置：</p>
        <pre style={{ background: "#f5f5f5", padding: "8px", borderRadius: "4px" }}>
          {JSON.stringify(apiConfig, null, 2)}
        </pre>
      </div>
      <CommonTable
        {...args}
        columns={sortedColumns}
        showColumnSetting={true}
        tableKey="api-sync-demo-table"
        columnsState={{
          value: columnsState,
          onChange: setColumnsMap,
          persistenceType: "api",
          api: apiConfig
        }}
        defaultColumnsState={createDefaultColumnsState(columns, defaultHiddenColumns, defaultLeftFixedColumns, defaultRightFixedColumns)}
      />
    </div>
  );
};

export const ApiSyncColumns = {
  render: ApiSyncColumnsTemplate,
  args: {
    tableListData: {
      list: [
        {
          deviceName: "TDEV-1011-41",
          groupName: "测试专用分组",
          groupName2: "设备分组A",
          productModelNo: "pm00003",
          realtimeStatus: "OFFLINE",
          realtimeStatusName: "离线",
          deviceRestElectric: "85%",
        },
        {
          deviceName: "TDEV-1011-39",
          groupName: "测试专用分组",
          groupName2: "设备分组B",
          productModelNo: "pm00003",
          realtimeStatus: "ONLINE",
          realtimeStatusName: "在线",
          deviceRestElectric: "72%",
        },
        {
          deviceName: "TDEV-1011-38",
          groupName: "测试专用分组",
          groupName2: "设备分组C",
          productModelNo: "pm00004",
          realtimeStatus: "ONLINE",
          realtimeStatusName: "在线",
          deviceRestElectric: "63%",
        },
      ],
      totalNumber: 3,
      totalPage: 1,
    },
    loading: false,
    rowKey: "deviceName",
  },
  parameters: {
    docs: {
      description: {
        story: '这个示例展示了通过 API 同步列配置的功能。在实际应用中，这允许用户在多个设备之间同步他们的列配置。',
      },
    },
  },
};

export const DarkTheme: Story = {
  args: {
    theme: "dark",
    tableListData: {
      list: [
        {
          deviceName: "TDEV-1011-41",
          groupName: "测试专用分组",
          productModelNo: "pm00003",
          realtimeStatus: "OFFLINE",
          realtimeStatusName: "离线",
        },
        {
          deviceName: "TDEV-1011-39",
          groupName: "测试专用分组",
          productModelNo: "pm00003",
          realtimeStatus: "OFFLINE",
          realtimeStatusName: "离线",
        },
      ],
      totalNumber: 0,
      totalPage: 0,
    },
    columns: [
      {
        title: "序号",
        dataIndex: "order",
        align: "center" as const,
        width: 120,
        render: (_text: any, _record: any, index: number) => index + 1,
      },
      {
        title: "设备分组",
        dataIndex: "groupName",
        align: "center" as const,
        ellipsis: true,
      },
      {
        title: "型号",
        dataIndex: "productModelNo",
        align: "center" as const,
        ellipsis: true,
      },
      {
        title: "分组",
        dataIndex: "groupName",
        align: "center" as const,
        ellipsis: true,
      },
      {
        title: "SN",
        dataIndex: "deviceName",
        align: "center" as const,
        ellipsis: true,
      },
      {
        title: "电量",
        dataIndex: "deviceRestElectric",
        align: "center" as const,
        ellipsis: true,
      },
    ],
    loading: false,
    rowKey: "id",
  },
};
