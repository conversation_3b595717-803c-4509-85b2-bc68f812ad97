import React, { useState, useCallback } from "react";
import { Upload, Button, message, Progress, Space, Modal } from "antd";
import { DownloadOutlined } from "@ant-design/icons";
import type { UploadFile } from "antd/es/upload/interface";
import { ExcelUploaderProps, ImportResult } from "./types";
import { ExcelParser } from "./ExcelParser";
import { ErrorExporter } from "./ErrorExporter";
import ImportErrorModal from "./ImportErrorModal";
import FileUpload from "@components/FileUpload";
import "./ExcelUploader.scss";
const ExcelUploader: React.FC<ExcelUploaderProps> = ({
  downLoadTemplate,
  columnConfig = [],
  onSuccess,
  onError,
  customValidators,
  visible,
  onCancel,
  title = "批量上传",
  description = (
    <div>
      导入模板"<span style={{ color: "red" }}>*</span>"为必填项。
    </div>
  ),
  postProcessor,
  uploadToS3 = false,
  uploadMode = "dragger",
  showStepGuidance = false,
  downloadIconText = "批量上传模板",
  s3Config,
  draggerConfig,
  onS3UploadConfirm,
}) => {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [errorModalVisible, setErrorModalVisible] = useState(false);
  const [importResult, setImportResult] = useState<ImportResult>({
    totalCount: 0,
    successCount: 0,
    errorCount: 0,
  });
  const [errorWorkbook, setErrorWorkbook] = useState<any>(null);
  const parser = new ExcelParser(columnConfig, customValidators);
  const errorExporter = new ErrorExporter(columnConfig);

  // upload基础配置
  const baseProps = {
    maxCount: 1,
    accept: ".xlsx,.xls" as const,
  };

  // S3 上传配置
  const getS3UploadProps = useCallback(
    () => ({
      ...baseProps,
      fileListType: "file" as const,
      getPreSignatureUrl: s3Config?.getPreSignatureUrl || "",
      LOPDN: s3Config?.LOPDN || "",
      bucketName: s3Config?.bucketName || "",
      needMd5Validete: s3Config?.needMd5Validete || false,
      maxFileSize: s3Config?.maxFileSize || 50,
      unit: s3Config?.unit || ("MB" as const),
      uploadBtn: {
        type: uploadMode,
        btnText: "添加文件",
        draggerConfig: {
          title: draggerConfig?.title || "点击或者将文件拖拽到此处进行上传",
          hint: draggerConfig?.hint || "",
        },
      },
      onEnd: (fileKey: string) => {
        s3Config?.onS3Success?.(fileKey);
      },
      onError: (error: Error, body?: Object) => {
        s3Config?.onS3Error?.(error, body);
      },
      onDelete: (fileKey: string) => {
        s3Config?.onS3Delete?.(fileKey);
      },
      extensionValidate: (fileName: string) => {
        const validExtensions = [".xlsx", ".xls"];
        return validExtensions.some((ext) =>
          fileName.toLowerCase().endsWith(ext)
        );
      },
    }),
    [JSON.stringify(s3Config), uploadMode, JSON.stringify(draggerConfig)]
  );

  // 本地解析配置
  const getLocalUploadProps = useCallback(
    () => ({
      ...baseProps,
      fileList,
      beforeUpload: (file) => {
        setFileList([file]);
        return false;
      },
      onRemove: () => {
        setFileList([]);
      },
    }),
    [fileList]
  );

  const handleDownloadErrorData = () => {
    if (errorWorkbook) {
      errorExporter.downloadErrorFile(errorWorkbook);
      setErrorModalVisible(false);
    }
  };

  const handleUpload = async (file: File) => {
    setUploading(true);
    setProgress(0);
    setErrorWorkbook(null);
    setImportResult({
      totalCount: 0,
      successCount: 0,
      errorCount: 0,
    });
    try {
      const progressInterval = setInterval(() => {
        setProgress((prev) => Math.min(prev + 10, 90));
      }, 200);
      const { data } = await parser.parse(file);
      const rowObjects = data.map((row) => {
        const obj: any = {};
        columnConfig.forEach((col, index) => {
          obj[col.key] = row[index];
          if (col.transform) {
            obj[col.key] = col.transform(obj[col.key]);
          }
        });
        return obj;
      });
      // 1. 首先进行基础字段验证
      const { validData: basicValidData, invalidData: basicInvalidData } =
        parser.separateData(rowObjects);
      // 2. 如果有后处理函数，对通过基础验证的数据进行处理
      let finalValidData = basicValidData;
      let finalInvalidData = basicInvalidData;
      if (postProcessor && basicValidData.length > 0) {
        const { validData, invalidData, errors } = await postProcessor(
          basicValidData
        );
        finalValidData = validData;
        // 将不符合业务规则的数据添加到无效数据中
        finalInvalidData = [
          ...basicInvalidData,
          ...invalidData.map((item) => ({
            ...item,
            errors: errors || ["不符合业务规则"],
          })),
        ];
      }
      clearInterval(progressInterval);
      setProgress(100);
      // 3. 更新导入结果
      setImportResult({
        totalCount: rowObjects.length,
        successCount: finalValidData.length,
        errorCount: finalInvalidData.length,
      });
      // 4. 处理错误数据导出
      if (finalInvalidData.length > 0) {
        const workbook = errorExporter.generateErrorSheet(finalInvalidData);
        setErrorWorkbook(workbook);
        setErrorModalVisible(true);
      }
      // 5. 处理成功回调
      if (finalValidData.length > 0) {
        onSuccess?.(finalValidData);
        message.success(`成功导入 ${finalValidData.length} 条数据`);
        onCancel();
      }
    } catch (error: any) {
      onError?.(error);
      message.error("文件处理失败");
    } finally {
      setUploading(false);
      setFileList([]);
    }
  };

  const renderGuidanceText = useCallback(
    (showStepGuidance: boolean, isDownload: boolean) => {
      if (isDownload) {
        return showStepGuidance ? (
          <div className="step-item">
            <span className="step-number active">1</span>
            <div className="step-content">
              <div className="step-title">第一步骤</div>
              <div className="step-description">下载模板</div>
            </div>
          </div>
        ) : (
          <span className="label">下载模板：</span>
        );
      } else {
        return showStepGuidance ? (
          <div className="step-item last-step-item">
            <span className="step-number last-step">2</span>
            <div className="step-content">
              <div className="step-title">第二步骤</div>
              <div className="step-description">按照模板填写后上传</div>
            </div>
          </div>
        ) : (
          <span className="label">上传文件：</span>
        );
      }
    },
    [showStepGuidance]
  );

  return (
    <>
      <Modal
        title={title}
        open={visible}
        onCancel={onCancel}
        footer={null}
        width={520}
        maskClosable={false}
        className="excel-uploader-modal"
      >
        <div className="excel-uploader-content">
          <div className="upload-section">
            <div className="upload-item center">
              <>{renderGuidanceText(showStepGuidance, true)}</>
              <Button
                type="link"
                icon={<DownloadOutlined />}
                onClick={downLoadTemplate}
              >
                {downloadIconText}
              </Button>
            </div>

            <div
              className={`upload-item ${
                uploadToS3 && (uploadMode === "dragger" || showStepGuidance)
                  ? "vertical"
                  : "horizontal"
              }`}
            >
              <>{renderGuidanceText(showStepGuidance, false)}</>
              {uploadToS3 ? (
                // S3 上传模式
                <div className="s3-upload-container">
                  <FileUpload {...getS3UploadProps()} />
                </div>
              ) : (
                // 本地解析模式
                <Upload {...getLocalUploadProps()}>
                  <Button>添加文件</Button>
                </Upload>
              )}
            </div>

            {description && (
              <div className="upload-description">
                <span>说明：</span>
                <p>{description}</p>
              </div>
            )}
          </div>

          {uploading && <Progress percent={progress} />}

          <div className="modal-footer">
            <Button onClick={onCancel}>取消</Button>
            {uploadToS3 ? (
              // S3 上传模式
              <Button
                type="primary"
                onClick={() =>
                  onS3UploadConfirm && onS3UploadConfirm(fileList?.[0] as any)
                }
                disabled={fileList.length === 0 || uploading}
              >
                确定
              </Button>
            ) : (
              // 本地解析模式
              <Button
                type="primary"
                onClick={() => fileList[0] && handleUpload(fileList[0] as any)}
                disabled={fileList.length === 0 || uploading}
                loading={uploading}
              >
                {uploading ? "处理中" : "上传"}
              </Button>
            )}
          </div>
        </div>
      </Modal>
      <ImportErrorModal
        visible={errorModalVisible}
        onClose={() => setErrorModalVisible(false)}
        importResult={importResult}
        onDownload={handleDownloadErrorData}
      />
    </>
  );
};

export default ExcelUploader;
