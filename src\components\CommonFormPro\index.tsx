import React, { useEffect, useState, useRef } from "react";
import {
  DatePicker,
  Select,
  Form,
  Cascader,
  Input,
  Radio,
  Checkbox,
  Switch,
  Row,
  Col,
  Button,
  InputNumber,
  TimePicker,
  ConfigProvider,
} from "antd";
import locale from "antd/es/date-picker/locale/zh_CN";
import type { FormProConfig } from "./formConfig";
import "./index.scss";
import { addGlobalEventListener, removeGlobalEventListener } from "@utils/emit";
import { filterOption } from "@utils/index";
import FileUpload from "@components/FileUpload";
import { FieldItem } from "@components/CommonForm/formConfig";
const { RangePicker } = DatePicker;

export interface FormProps {
  name?: string;
  formConfig: FormProConfig;
  layout?: "horizontal" | "vertical" | "inline";
  defaultValue?: {
    [key in string]: any;
  };
  initLink?: boolean;
  formType?: "search" | "edit"; // search表示是搜索表单会有查询重置按钮
  colon?: boolean;
  className?: string;
  labelAlign?: "left" | "right" | undefined;
  onValueChange?: Function;
  getFormInstance?: Function;
  onResetClick?: Function;
  onSearchClick?: Function;
  onFieldFocus?: Function;
  getUploadStatus?: (
    status: boolean,
    uploadStatus: "uploading" | "error" | "success" | "delete",
    fieldName?: string
  ) => void;
}
const CommonFormPro = (props: FormProps) => {
  const {
    name,
    formConfig,
    layout = "horizontal",
    formType = "edit",
    labelAlign,
    className,
    colon,
    defaultValue = {},
    onValueChange,
    getFormInstance,
    onResetClick,
    onSearchClick,
    onFieldFocus,
    initLink = false,
  } = props;
  const [formData, setFormData] = useState<any>(null);
  const formDataRef = useRef<any>(null);
  const [commonFormRef] = Form.useForm();
  const [selectList, setSelectList] = useState<any>({});
  const [uploading, setUploading] = useState(false);
  const [, forceUpload] = useState<number>(0);
  const showCheckedStrategyMap = new Map([
    ["SHOW_PARENT", Cascader.SHOW_PARENT],
    ["SHOW_CHILD", Cascader.SHOW_CHILD],
  ]);
  useEffect(() => {
    setFormData(formConfig);
    formDataRef.current = formConfig;
    const cb = (data: { name: string; config: FormProConfig }) => {
      if (data.name === props.name) {
        setFormData({ ...data.config });
        formDataRef.current = { ...data.config };
      }
    };
    const updateOneConfig = (data: {
      name: string;
      fieldName: string;
      config: any;
    }) => {
      if (data.name === props.name) {
        formDataRef.current = {
          ...formDataRef.current,
          fields: formDataRef.current.fields?.map((v: any) => {
            if (v.fieldName === data.fieldName) {
              return data.config;
            } else {
              return v;
            }
          }),
        };
        setFormData({ ...formDataRef.current });
      }
    };
    addGlobalEventListener("FORCE_UPDATE_CONFIG", cb);
    addGlobalEventListener("FORCE_UPDATE_ONE_CONFIG", updateOneConfig);
    return () => {
      removeGlobalEventListener("FORCE_UPDATE_CONFIG", cb);
      removeGlobalEventListener("FORCE_UPDATE_ONE_CONFIG", updateOneConfig);
    };
  }, [JSON.stringify(formConfig)]);

  useEffect(() => {
    commonFormRef.setFieldsValue(defaultValue);
    defaultValue &&
      Object.keys(defaultValue).forEach((v) => {
        onFieldsChange(v, defaultValue[v], initLink ? "operate" : "init");
      });
    forceUpload(Date.now());
  }, [JSON.stringify(defaultValue)]);

  useEffect(() => {
    getFormInstance && getFormInstance(commonFormRef);
  }, []);

  const renderFieldItem = (dataItem: FieldItem) => {
    const {
      fieldName,
      type,
      options,
      placeholder,
      multiple,
      showSearch,
      maxLength,
      allowClear,
      disabled,
      showSelectAll,
      autoSize,
      showCount,
      maxTagCount,
      defaultChecked,
      mapRelation,
      renderFunc,
      changeOnSelect,
      showCheckedStrategy,
      labelInValue = true,
      showNow = false,
      min = 0,
      max = 9999999,
      disabledDate,
      disabledTime,
      fileListType = "picture",
      uploadedFileList,
      accept,
      bucketName,
      LOPDN,
      getPreSignatureUrl,
      maxFileSize,
      unit = "MB",
      needConfirm = false,
      format,
      hideDisabledOptions = false,
      showTime = true,
    } = dataItem;
    switch (type) {
      case "input":
        return (
          <Input
            placeholder={placeholder ?? ""}
            allowClear={typeof allowClear === "undefined" ? true : allowClear}
            maxLength={maxLength}
            disabled={disabled}
          />
        );
      case "inputNumber":
        return (
          <InputNumber
            placeholder={placeholder ?? ""}
            type="number"
            controls={false}
            min={min}
            max={max}
            maxLength={maxLength}
            disabled={disabled}
          />
        );
      case "radioGroup":
        return <Radio.Group options={options} disabled={disabled} />;
      case "textarea":
        return (
          <Input.TextArea
            placeholder={placeholder ?? ""}
            autoSize={autoSize === true ? { minRows: 2, maxRows: 6 } : autoSize}
            maxLength={maxLength}
            disabled={disabled}
            showCount={
              showCount && {
                formatter: ({ value, count, maxLength }) =>
                  `${count}/${maxLength}`,
              }
            }
          />
        );
      case "select":
        let _selectOptions: any[] = [];
        if (selectList[fieldName!]) {
          _selectOptions = selectList[fieldName!];
        } else if (options) {
          _selectOptions = options;
        }
        return (
          <Select
            placeholder={placeholder ?? ""}
            options={_selectOptions}
            showSearch={showSearch}
            labelInValue={labelInValue}
            disabled={disabled}
            mode={multiple ? "multiple" : undefined}
            allowClear={typeof allowClear === "undefined" ? true : allowClear}
            filterOption={showSearch && filterOption}
            onFocus={() => {
              onFieldFocus &&
                onFieldFocus(
                  dataItem.fieldName,
                  commonFormRef.getFieldsValue()
                );
            }}
          />
        );
      case "rangeTime":
        return (
          <RangePicker
            locale={locale}
            showTime={showTime}
            format={format ?? "YYYY-MM-DD HH:mm:ss"}
            disabled={disabled}
            showNow={showNow}
            needConfirm={needConfirm}
            // disabledDate={disabledDate}
          />
        );
      case "timePicker":
        return (
          <TimePicker
            disabled={disabled}
            disabledDate={disabledDate}
            disabledTime={disabledTime}
            showNow={showNow}
            needConfirm={needConfirm}
            hideDisabledOptions={hideDisabledOptions}
          />
        );
      case "datePicker":
        return (
          <DatePicker
            allowClear={typeof allowClear === "undefined" ? true : allowClear}
            locale={locale}
            format={format ?? "YYYY-MM-DD"}
            disabled={disabled}
            disabledDate={disabledDate}
            disabledTime={disabledTime}
            needConfirm={needConfirm}
          />
        );
      case "dateTime":
        return (
          <DatePicker
            allowClear={typeof allowClear === "undefined" ? true : allowClear}
            locale={locale}
            showTime={showTime}
            format={format ?? "YYYY-MM-DD HH:mm:ss"}
            disabled={disabled}
            disabledDate={disabledDate}
            disabledTime={disabledTime}
            needConfirm={needConfirm}
          />
        );
      case "cascader":
        let _cascaderOptions: any[] = [];
        if (selectList[fieldName!]) {
          _cascaderOptions = selectList[fieldName!];
        } else if (options) {
          _cascaderOptions = options;
        }
        return (
          <Cascader
            fieldNames={mapRelation}
            options={_cascaderOptions}
            placeholder={placeholder ?? ""}
            maxTagCount={maxTagCount}
            showSearch={typeof showSearch === "undefined" ? true : showSearch}
            disabled={disabled}
            allowClear={typeof allowClear === "undefined" ? true : allowClear}
            multiple={multiple}
            showCheckedStrategy={
              typeof showCheckedStrategy == "undefined"
                ? Cascader.SHOW_CHILD
                : showCheckedStrategyMap.get(showCheckedStrategy)
            }
            changeOnSelect={
              typeof changeOnSelect === "undefined" ? true : changeOnSelect
            }
            onDropdownVisibleChange={(value) => {
              onFieldFocus &&
                onFieldFocus(
                  dataItem.fieldName,
                  commonFormRef.getFieldsValue()
                );
            }}
          />
        );
      case "switch":
        return <Switch defaultChecked={defaultChecked} />;
      case "checkboxGroup":
        return <Checkbox.Group options={options} disabled={disabled} />;
      case "ReactNode":
        const fieldValue = commonFormRef.getFieldValue(dataItem.fieldName);
        return <>{renderFunc && renderFunc(dataItem, fieldValue)}</>;
      case "upload":
        return (
          <FileUpload
            accept={accept!}
            bucketName={bucketName}
            fileListType={fileListType}
            LOPDN={LOPDN!}
            getPreSignatureUrl={getPreSignatureUrl!}
            uploadedFileList={uploadedFileList || []}
            uploadBtn={{
              type: fileListType === "picture" ? "icon" : "btn",
              btnText: fileListType === "picture" ? "上传图片" : "上传文件",
            }}
            maxCount={max}
            maxFileSize={maxFileSize}
            unit={unit}
            onStart={() => {
              setUploading(true);
              props.getUploadStatus &&
                props.getUploadStatus(true, "uploading", fieldName);
            }}
            onEnd={(fileKey: string) => {
              const filekeyList = commonFormRef.getFieldValue(fieldName) || [];
              const set = new Set(filekeyList);
              set.add(fileKey);
              commonFormRef.setFieldValue(fieldName, [...set]);
              setUploading(false);
              props.getUploadStatus &&
                props.getUploadStatus(false, "success", fieldName);
            }}
            onDelete={(fileKey: string) => {
              const filekeyList = commonFormRef.getFieldValue(fieldName) || [];
              const set = new Set(filekeyList);
              set.delete(fileKey);
              commonFormRef.setFieldValue(fieldName, [...set]);
              props.getUploadStatus &&
                props.getUploadStatus(false, "delete", fieldName);
            }}
            onError={() => {
              setUploading(false);
              props.getUploadStatus &&
                props.getUploadStatus(false, "error", fieldName);
            }}
          />
        );
      case "text":
        const value = commonFormRef.getFieldValue(dataItem.fieldName);
        return <span className="field-text">{value}</span>;
      default:
        return null;
    }
  };
  const getFieldByFieldName = (fieldName: string) => {
    return formDataRef.current.fields?.find(
      (f: FieldItem) => f.fieldName === fieldName
    );
  };
  const onFieldsChange = (
    changedFieldName: any,
    val: any,
    type: "init" | "operate"
  ) => {
    if (!formDataRef.current?.linkRules) {
      return;
    }
    // 找到当前元素改变后需要的联动的表单项
    const hasLinkRule = Object.keys(formDataRef.current?.linkRules).includes(
      changedFieldName
    );
    if (!hasLinkRule) {
      return;
    }
    formDataRef.current?.linkRules[changedFieldName]?.forEach((item: any) => {
      const {
        targetFields,
        rule,
        dependenceData,
        disabledValue,
        fetchFunc,
        refreshFunc,
      } = item;
      switch (rule) {
        case "visible":
          targetFields?.forEach((targetField: string) => {
            const field = getFieldByFieldName(targetField);
            if (field) {
              field.hidden = !dependenceData.includes(val?.value ?? val);
            }
          });
          break;
        case "visibility":
          if (type == "operate") {
            targetFields?.forEach((targetField: string) => {
              const field = getFieldByFieldName(targetField);
              if (field && dependenceData.includes(val?.value ?? val)) {
                field.hidden = false;
              }
            });
          }

          break;
        case "hidden":
          if (type == "operate") {
            targetFields?.forEach((targetField: string) => {
              const field = getFieldByFieldName(targetField);
              if (field && dependenceData.includes(val?.value ?? val)) {
                field.hidden = true;
              }
            });
          }

          break;
        case "refresh":
          if (refreshFunc) {
            targetFields?.forEach((targetField: string) => {
              const field = getFieldByFieldName(targetField);
              if (field) {
                const data = refreshFunc(val, field);
                field.options = data;
                commonFormRef.setFieldsValue({
                  [targetField]: defaultValue[targetField],
                });
              }
            });
          }
          break;
        case "clear":
          if (type === "operate") {
            targetFields?.forEach((targetField: string) => {
              const field = getFieldByFieldName(targetField);
              commonFormRef.setFieldsValue({
                [targetField]: field?.multiple ? undefined : null,
              });
            });
          }
          break;
        case "fetchData":
          fetchFunc(val, commonFormRef).then((res: any[]) => {
            setSelectList((selectList: any) => ({
              ...selectList,
              [targetFields && targetFields[0]]: res,
            }));
          });
          break;
        case "valueDisable":
          targetFields?.forEach((targetField: string) => {
            const field = getFieldByFieldName(targetField);
            if (field) {
              field.options = field?.options?.map((v: any) => {
                if (
                  disabledValue.includes(v.value) &&
                  dependenceData.includes(val)
                ) {
                  return {
                    ...v,
                    disabled: true,
                  };
                } else {
                  return { ...v, disabled: false };
                }
              });
            }
          });

          break;
        case "fieldItemDisable":
          targetFields?.forEach((targetField: string) => {
            const field = getFieldByFieldName(targetField);
            if (field) {
              field.disabled = dependenceData.includes(val);
            }
          });
          break;
      }
    });
    setFormData({ ...formDataRef.current });
  };

  const makeLayout = () => {
    let titleMaxLength = 4;
    formData?.fields.forEach((item: any) => {
      if (item.label?.length && item.label?.length > titleMaxLength) {
        titleMaxLength = item.label.length || item.label.length;
      }
    });
    titleMaxLength = titleMaxLength >= 9 ? 9 : titleMaxLength;

    return {
      labelCol: { span: titleMaxLength + 2 },
      wrapperCol:
        layout === "inline"
          ? { span: 24 - (titleMaxLength + 2) }
          : { span: 23 - (titleMaxLength + 1) },
    };
  };

  return (
    <ConfigProvider prefixCls="x-coreui">
      <div
        className={
          formType === "search"
            ? "searchform-container"
            : "common-form-container"
        }
      >
        <Form
          {...makeLayout()}
          initialValues={defaultValue}
          name={name || "complex-form"}
          labelAlign={labelAlign ?? "right"}
          layout={layout}
          colon={colon ?? true}
          form={commonFormRef}
          autoComplete="off"
          className={className || ""}
          style={{
            alignItems: "center",
          }}
          onValuesChange={(changedValues, allValues) => {
            const changedFieldName = Object.keys(changedValues)[0];
            const changedVal = changedValues[changedFieldName];
            onValueChange &&
              onValueChange(commonFormRef.getFieldsValue(), changedFieldName);
            changedFieldName &&
              onFieldsChange(changedFieldName, changedVal, "operate");
          }}
        >
          <Row gutter={24} align="middle" style={{ width: "100%" }}>
            {formData?.fields?.map((item: FieldItem, index: number) => {
              if (item.childrenList) {
                const cWidth = Math.floor(100 / item.childrenList.length);
                return (
                  !item.hidden && (
                    <Col
                      xxl={layout === "inline" ? (item.xxl ? item.xxl : 4) : 23}
                      xl={layout === "inline" ? (item.xl ? item.xl : 6) : 23}
                      lg={layout === "inline" ? (item.lg ? item.lg : 8) : 23}
                      md={item.md ? item.md : 23}
                      key={index}
                    >
                      <Form.Item
                        label={item.label}
                        style={{ marginBottom: 0 }}
                        labelCol={item.labelCol && item.labelCol}
                        wrapperCol={item.wrapperCol && item.wrapperCol}
                        tooltip={item.tooltip && item.tooltip}
                      >
                        {item.childrenList.map(
                          (name: string, index: number) => {
                            const _cField = formData?.fields.find(
                              (v: FieldItem) => v.fieldName === name
                            );
                            const _marginLeft =
                              _cField?.marginLeft || _cField?.marginLeft === 0
                                ? _cField.marginLeft
                                : 8;
                            const _marginRight =
                              _cField?.marginRight || _cField?.marginRight === 0
                                ? _cField.marginRight
                                : 8;
                            return (
                              <Form.Item
                                labelCol={_cField?.labelCol && _cField.labelCol}
                                wrapperCol={
                                  _cField?.wrapperCol && _cField.wrapperCol
                                }
                                name={_cField?.fieldName}
                                label={_cField?.label}
                                help={_cField?.help}
                                rules={_cField?.validatorRules ?? []}
                                className={
                                  "field_" + _cField?.fieldName?.toString()
                                }
                                style={{
                                  display: "inline-block",
                                  width:
                                    _cField?.width ??
                                    `calc(${cWidth}% - ${_marginLeft}px - ${_marginRight}px)`,
                                  marginLeft: `${_marginLeft}px`,
                                  marginRight: `${_marginRight}px`,
                                }}
                              >
                                {renderFieldItem(_cField)}
                              </Form.Item>
                            );
                          }
                        )}
                      </Form.Item>
                    </Col>
                  )
                );
              } else if (!item.isChild) {
                return (
                  !item.hidden && (
                    <Col
                      xxl={layout === "inline" ? (item.xxl ? item.xxl : 4) : 23}
                      xl={layout === "inline" ? (item.xl ? item.xl : 6) : 23}
                      lg={layout === "inline" ? (item.lg ? item.lg : 8) : 23}
                      md={item.md ? item.md : 23}
                      key={item.fieldName?.toString()}
                    >
                      <Form.Item
                        labelCol={item.labelCol && item.labelCol}
                        wrapperCol={item.wrapperCol && item.wrapperCol}
                        name={item.fieldName}
                        label={item.label}
                        help={item.help}
                        rules={item.validatorRules ?? []}
                        className={"field_" + item.fieldName?.toString()}
                        tooltip={item.tooltip && item.tooltip}
                      >
                        {renderFieldItem(item)}
                      </Form.Item>
                    </Col>
                  )
                );
              }
            })}
          </Row>
        </Form>
        {formType === "search" && (
          <Row justify={"end"} className="searchbtns">
            <Button
              className="reset"
              style={{ marginRight: "12px" }}
              onClick={() => {
                commonFormRef.setFieldsValue(defaultValue);
                Object.keys(defaultValue).forEach((v) => {
                  onFieldsChange(v, defaultValue[v], "operate");
                });
                onResetClick && onResetClick();
              }}
            >
              重置
            </Button>

            <Button
              type="primary"
              onClick={() => {
                onSearchClick && onSearchClick(commonFormRef.getFieldsValue());
              }}
            >
              查询
            </Button>
          </Row>
        )}
      </div>
    </ConfigProvider>
  );
};

export type { FormProConfig };
export default React.memo(CommonFormPro);
