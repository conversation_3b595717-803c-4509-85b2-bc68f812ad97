import React, { useEffect, useState } from "react";
import "./index.scss";
import { Button, ConfigProvider } from "antd";
import { AnyFunc } from "../../global";
export interface ButtonItem {
  label: string;
  isActive: boolean;
  value: any;
}
interface ButtonRadioProps {
  btnGroup: ButtonItem[];
  needConfirm?: boolean;
  onChange: AnyFunc;
}
const ButtonRadio = (props: ButtonRadioProps) => {
  const { btnGroup, needConfirm = false } = props;
  const findIndex = btnGroup.findIndex(
    (item: ButtonItem) => item.isActive === true
  );
  const [activeBtnIndex, setActiveBtnIndex] = useState(findIndex);
  const onChange = (selectItem: ButtonItem, index: number, evt: any) => {
    if (!needConfirm) {
      const btnNode = evt.currentTarget;
      if (btnNode.className.includes("btn-active")) {
        return;
      }
      setActiveBtnIndex(index);
    }
    props.onChange && props.onChange(selectItem);
  };

  return (
    <ConfigProvider prefixCls="x-coreui">
      <div className="btn-group">
        {props.btnGroup.map((item: any, index: number) => (
          <Button
            key={index}
            type={index === activeBtnIndex ? "primary" : "default"}
            onClick={onChange.bind(null, item, index)}
          >
            {item.label}
          </Button>
        ))}
      </div>
    </ConfigProvider>
  );
};

export default React.memo(ButtonRadio);
