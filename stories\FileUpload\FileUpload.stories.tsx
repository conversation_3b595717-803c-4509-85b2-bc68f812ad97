import type { Meta } from "@storybook/react";
import type { StoryObj } from "@storybook/react";
import FileUpload from "./FileUpload";

const meta: Meta<typeof FileUpload> = {
  title: "FileUpload",
  tags: ["autodocs"],
  component: FileUpload,
  argTypes: {},
};

export default meta;
type Story = StoryObj<typeof FileUpload>;
export const Primary: Story = {
  args: {
    accept: ".zip",
    getPreSignatureUrl: "https://uat-api-cloud.jdl.cn/k2/oss/upload",
    btnWidth: 80,
    btnHeight: 80,
    uploadBtn: {
      type: "btn",
      btnText: "上传文件",
    },
    maxCount: 5,
    maxFileSize: 50,
    unit: "MB",
    bucketName: "rover-operation",
    LOPDN: "device.web.public.jsf.beta",
  },
};
