import type { Meta } from "@storybook/react";
import type { StoryObj } from "@storybook/react";
import { Login } from "./PassportLogin";

const meta: Meta<typeof Login> = {
  title: "PassportLogin",
  tags: ["autodocs"],
  component: Login,
  argTypes: {},
};

export default meta;
type Story = StoryObj<typeof Login>;
export const Example: Story = {
  args: {
    returnUrl: "https://jdxmonitor.jdl.cn/",
  },
};
