import React, { useRef, useState } from 'react';
import dayjs, { Dayjs } from 'dayjs';
import classNames from 'classnames';
import { DateType } from '../type';
import { isEqual } from 'lodash';
import { AnyFunc } from '../../../global';
export interface DateCellProps {
  type: DateType;
  defaultDate: Dayjs;
  onSelect: AnyFunc;
  maxSize: number;
  selectedList: { value: string | number }[];
  list: {
    value: string;
    text: string;
    subText?: string;
    enable?: boolean;
  }[];
}

const DateCell: React.FC<DateCellProps> = (props) => {
  const { type, defaultDate, list, onSelect, selectedList, maxSize } = props;
  const _defaultDate =
    type === DateType.MONTH
      ? `${defaultDate.year()}-${defaultDate.month() + 1}`
      : `${defaultDate.year()}-${defaultDate.week()}`;
  return (
    <div
      className="date-cell-list"
      onMouseUp={(e) => {
        e.stopPropagation();
        e.preventDefault();
        const selection = window.getSelection()!;
        const selectionStr = selection.toString().replace(/\s+/g, '');
        const originSelectedList = [...selectedList];
        const newSelected = list.filter(
          (i: any) => selectionStr.indexOf(i.text) > -1,
        );
        newSelected?.forEach((i) => {
          const findObj = originSelectedList.find(
            (item) => item.value === i.value,
          );
          if (!findObj) {
            originSelectedList.push(i);
          }
        });
        selection.removeAllRanges();
        !isEqual(originSelectedList, selectedList) &&
          onSelect(originSelectedList);
      }}
    >
      {list?.map((item: any) => (
        <div
          key={item.value}
          className={classNames('cell-item', {
            'cell-active': _defaultDate === `${item.year}-${item.value}`,
            'cell-selected': selectedList?.find(
              (i: any) => i.year === item.year && i.value === item.value,
            ),
          })}
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            const index = selectedList?.findIndex(
              (i) => i.value === item.value,
            );
            if (index < 0) {
              maxSize === 1
                ? onSelect([item])
                : onSelect([...selectedList, item]);
            } else {
              onSelect(selectedList?.filter((i) => i.value != item.value));
            }
          }}
        >
          <div className="cell-item-text">{item.text}</div>
          {item.subText && (
            <div className="cell-item-subtext">{item.subText}</div>
          )}
        </div>
      ))}
    </div>
  );
};

export default DateCell;
