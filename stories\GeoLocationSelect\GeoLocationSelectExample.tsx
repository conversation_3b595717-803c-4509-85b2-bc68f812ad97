import React, { useState } from "react";
import GeoLocationSelect, { DockPoint, LocationPoint } from "@components/GeoLocationSelect";
import { Button, Space } from "antd";
import "./index.scss";

interface GeoLocationSelectExampleProps {
  dockPoints: DockPoint[];
  defaultActiveTab?: 'dockPoint' | 'location';
  placeholder?: string;
  useCustomRender?: boolean;
}

export const GeoLocationSelectExample: React.FC<GeoLocationSelectExampleProps> = ({
  dockPoints,
  defaultActiveTab = 'dockPoint',
  placeholder = '请输入地点名称搜索',
  useCustomRender = false,
}) => {
  const [selectedValue, setSelectedValue] = useState<any>(null);
  const [selectedOption, setSelectedOption] = useState<any>(null);

  // 演示外部控制值的示例
  const [startPoint, setStartPoint] = useState<any>(null);
  const [endPoint, setEndPoint] = useState<any>(null);

  const handleChange = (value: any, option: any) => {
    setSelectedValue(value);
    setSelectedOption(option);
    console.log('选中值:', value);
    console.log('选中选项:', option);
  };

  // 处理起点选择
  const handleStartPointChange = (value: any, option: any) => {
    setStartPoint(value);
  };

  // 处理终点选择
  const handleEndPointChange = (value: any, option: any) => {
    setEndPoint(value);
  };

  // 切换起点和终点
  const switchPoints = () => {
    const temp = startPoint;
    setStartPoint(endPoint);
    setEndPoint(temp);
  };

  const customOptionRender = useCustomRender ?
    (item: DockPoint | LocationPoint, tabType: 'dockPoint' | 'location') => {
      if (tabType === 'dockPoint') {
        const dockPoint = item as DockPoint;
        return (
          <div className="custom-option">
            <div className="custom-option-icon">🚢</div>
            <div className="custom-option-content">
              <div className="custom-option-title">{dockPoint.name}</div>
              {dockPoint.address && <div className="custom-option-address">{dockPoint.address}</div>}
            </div>
          </div>
        );
      } else {
        const locationPoint = item as LocationPoint;
        return (
          <div className="custom-option">
            <div className="custom-option-icon">📍</div>
            <div className="custom-option-content">
              <div className="custom-option-title">{locationPoint.title}</div>
              {locationPoint.address && <div className="custom-option-address">{locationPoint.address}</div>}
            </div>
          </div>
        );
      }
    } : undefined;

  return (
    <div className="geo-location-select-example">
      <h3>基本用法</h3>
      <div className="select-container">
        <GeoLocationSelect
          dockPoints={dockPoints}
          defaultActiveTab={defaultActiveTab}
          placeholder={placeholder}
          onChange={handleChange}
          optionRender={customOptionRender}
        />
      </div>

      {selectedValue && (
        <div className="selected-info">
          <h4>已选择:</h4>
          <pre>{JSON.stringify(selectedOption?.item || {}, null, 2)}</pre>
        </div>
      )}

      <h3 style={{ marginTop: '30px' }}>外部控制值示例（起点/终点切换）</h3>
      <div className="route-container">
        <div className="route-selects">
          <div className="route-select">
            <label>起点：</label>
            <GeoLocationSelect
              dockPoints={dockPoints}
              placeholder="请选择起点"
              onChange={handleStartPointChange}
              value={startPoint}
            />
          </div>
          <div className="route-select">
            <label>终点：</label>
            <GeoLocationSelect
              dockPoints={dockPoints}
              placeholder="请选择终点"
              onChange={handleEndPointChange}
              value={endPoint}
            />
          </div>
        </div>
        <div className="route-actions">
          <Button type="primary" onClick={switchPoints}>
            切换起点和终点
          </Button>
        </div>
      </div>
    </div>
  );
};
