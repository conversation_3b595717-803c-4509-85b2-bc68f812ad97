.geo-location-select-example {
  width: 500px;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 8px;

  h3 {
    margin-top: 0;
    margin-bottom: 16px;
  }

  .select-container {
    margin-bottom: 20px;
  }

  .selected-info {
    margin-top: 20px;
    padding: 12px;
    background-color: #fff;
    border-radius: 4px;

    h4 {
      margin-top: 0;
      margin-bottom: 8px;
    }

    pre {
      margin: 0;
      padding: 8px;
      background-color: #f0f0f0;
      border-radius: 4px;
      overflow: auto;
    }
  }

  .custom-option {
    display: flex;
    align-items: flex-start;
    padding: 4px 0;

    .custom-option-icon {
      margin-right: 8px;
      font-size: 16px;
    }

    .custom-option-content {
      flex: 1;
    }

    .custom-option-title {
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
    }

    .custom-option-address {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.45);
      line-height: 20px;
    }
  }

  .route-container {
    margin-top: 16px;
    padding: 16px;
    background-color: #fff;
    border-radius: 4px;

    .route-selects {
      margin-bottom: 16px;

      .route-select {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        label {
          width: 60px;
          margin-right: 8px;
          text-align: right;
          font-weight: 500;
        }

        .geo-location-select {
          flex: 1;
        }
      }
    }

    .route-actions {
      display: flex;
      justify-content: center;
      margin-top: 16px;
    }
  }
}
