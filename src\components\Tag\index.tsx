import React, { ReactNode, useState } from "react";
import classNames from "classnames";
import "./index.scss";
export interface TagProps {
  className?: string;
  children?: ReactNode;
  color?: string;
  icon?: ReactNode;
  style?: React.CSSProperties;
  hasClose?: boolean;
}
const Tag = (props: TagProps) => {
  const { className, children, color = "#333", icon, hasClose } = props;
  const mergedStyle: React.CSSProperties = {
    backgroundColor: color,
  };
  return (
    <span className={classNames("tag-item", className)} style={mergedStyle}>
      {icon && <i className="tag-item_icon" />}
      {children}
      {hasClose && <i className="close-icon" />}
    </span>
  );
};
export default Tag;