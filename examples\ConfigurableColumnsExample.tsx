import React, { useState } from 'react';
import CommonTable from '../src/components/CommonTable';
import { createDefaultColumnsState } from '../src/components/CommonTable/columnUtils';
import { Button } from 'antd';

const ConfigurableColumnsExample: React.FC = () => {
  // 模拟数据
  const dataSource = [
    {
      id: '1',
      name: '张三',
      age: 28,
      address: '北京市朝阳区',
      phone: '13800138000',
      email: 'z<PERSON><PERSON>@example.com',
      department: '研发部',
      position: '前端开发',
    },
    {
      id: '2',
      name: '李四',
      age: 32,
      address: '上海市浦东新区',
      phone: '13900139000',
      email: '<EMAIL>',
      department: '产品部',
      position: '产品经理',
    },
    {
      id: '3',
      name: '王五',
      age: 25,
      address: '广州市天河区',
      phone: '13700137000',
      email: '<EMAIL>',
      department: '设计部',
      position: 'UI设计师',
    },
  ];

  // 定义列
  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      render: (_: any, __: any, index: number) => index + 1,
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: 100,
    },
    {
      title: '年龄',
      dataIndex: 'age',
      key: 'age',
      width: 80,
    },
    {
      title: '地址',
      dataIndex: 'address',
      key: 'address',
      width: 200,
    },
    {
      title: '电话',
      dataIndex: 'phone',
      key: 'phone',
      width: 150,
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      width: 200,
    },
    {
      title: '部门',
      dataIndex: 'department',
      key: 'department',
      width: 120,
    },
    {
      title: '职位',
      dataIndex: 'position',
      key: 'position',
      width: 120,
    },
  ];

  // 默认隐藏的列
  const defaultHiddenColumns = ['phone', 'email'];
  
  // 列显示状态
  const [columnsState, setColumnsState] = useState(
    createDefaultColumnsState(columns, defaultHiddenColumns)
  );

  return (
    <div style={{ padding: '20px' }}>
      <h1>可配置列示例</h1>
      <p>点击右上角的设置图标可以配置显示/隐藏列</p>
      
      <CommonTable
        tableListData={{ list: dataSource, totalNumber: dataSource.length }}
        columns={columns}
        loading={false}
        rowKey="id"
        tableKey="demo-table"
        showColumnSetting={true}
        columnsState={{
          value: columnsState,
          onChange: setColumnsState,
          persistenceType: 'localStorage',
        }}
        defaultColumnsState={createDefaultColumnsState(columns, defaultHiddenColumns)}
      />
      
      <div style={{ marginTop: '20px' }}>
        <Button 
          type="primary" 
          onClick={() => {
            setColumnsState(createDefaultColumnsState(columns, defaultHiddenColumns));
          }}
        >
          重置列设置
        </Button>
      </div>
    </div>
  );
};

export default ConfigurableColumnsExample;
