import React, { ReactNode, useEffect, useState, useMemo } from "react";
import {
  Button,
  Popconfirm,
  Table as BaseTable,
  ConfigProvider as ConfigProvider5,
  Spin,
} from "antd";
import "./index.scss";
import { AnyFunc, AnyObject } from "../../global";
import ColumnSetting from "./ColumnSetting";
import { fetchColumnSettings, saveColumnSetting, ColumnSettingData, ColumnSettingServiceOptions } from "../../services/columnSettingService";
// 列表页面可选页数
export const defaultPageSizeOptions: string[] = ["10", "20", "30", "40", "100"];
export interface MiddleBtns {
  title: string;
  sourceCode?: string;
  btnType?: "primary" | "default" | "textBtn";
  onClick: Function;
  enablePopConfirm?: boolean;
  popConfirmContent?: string;
  clstag?: string; // 埋点坑位id
  loading?: boolean;
}
import { ColumnState } from "./ColumnSetting";

export interface TableProps<T> {
  className?: string;
  theme?: "light" | "dark";
  tableListData: { list: object[]; totalNumber?: number; totalPage?: number };
  columns: any[];
  loading?: boolean;
  rowKey: string; // table里面每一行的唯一标识key
  middleBtns?: MiddleBtns[] | null | undefined; // 中间按键
  searchRef?: any;
  searchCondition?: T;
  rowClassName?: (record: T, index: number) => string;
  onPageChange?: Function;
  expandable?: any; // 配置展开属性
  rowSelection?: object;
  notPage?: boolean;
  scrollY?: number;
  tableKey?: string;
  crossPageSelect?: AnyFunc;
  pageSizeOptions?: string[];
  summary?: (currentData: any) => ReactNode;
  // 列设置相关属性
  columnsState?: {
    value?: Record<string, ColumnState>;
    onChange?: (map: Record<string, ColumnState>) => void;
    persistenceKey?: string;
    persistenceType?: 'localStorage' | 'sessionStorage' | 'api';
    // API 相关配置
    api?: ColumnSettingServiceOptions;
  };
  // 是否显示列设置按钮
  showColumnSetting?: boolean;
  // 默认列设置
  defaultColumnsState?: Record<string, ColumnState>;
  // 列设置组件属性
  columnSettingProps?: {
    checkedReset?: boolean;
    checkable?: boolean;
    draggable?: boolean;
    showListItemOption?: boolean;
    listsHeight?: number;
  };
}

export const customLocale = {
  filterConfirm: "确定",
  filterReset: "重置",
  emptyText: "暂无数据",
  pagination: {
    items_per_page: "/页",
    jump_to: "跳至",
    page: "",
    all_item_text: "全部",
    next_page_text: "下一页",
    prev_page_text: "上一页",
    prev_5_text: "前 5 页",
    next_5_text: "后 5 页",
    first_page_text: "第一页",
    last_page_text: "最后一页",
  },
};

function CommonTable<T extends AnyObject = AnyObject>(props: TableProps<T>) {
  const {
    className,
    middleBtns,
    loading,
    columns,
    searchRef,
    tableListData,
    searchCondition,
    onPageChange,
    rowKey,
    expandable,
    rowSelection,
    notPage,
    scrollY,
    tableKey,
    theme = "light",
    crossPageSelect,
    rowClassName,
    summary,
    columnsState,
    showColumnSetting = false,
    defaultColumnsState,
    columnSettingProps,
  } = props;
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);

  // 生成列的唯一键
  const genColumnKey = (key?: React.Key, index?: number) => {
    if (key) {
      return key.toString();
    }
    return `${index}`;
  };

  // 加载状态
  const [isLoading, setIsLoading] = useState(false);

  // 获取默认列配置
  const getDefaultColumnsMap = () => {
    // 如果提供了初始值，使用初始值
    if (columnsState?.value) {
      return columnsState.value;
    }

    // 如果提供了默认列设置，使用默认设置
    if (defaultColumnsState) {
      return defaultColumnsState;
    }

    // 否则所有列默认显示
    return columns.reduce((prev, current, index) => {
      const key = genColumnKey(current.key || current.dataIndex, index);
      if (key) {
        prev[key] = {
          show: true,
          fixed: current.fixed,
          order: index
        };
      }
      return prev;
    }, {} as Record<string, ColumnState>);
  };

  // 从本地存储获取列配置
  const getColumnsMapFromStorage = () => {
    if (!tableKey) return null;

    const persistenceType = columnsState?.persistenceType;
    if (persistenceType !== 'localStorage' && persistenceType !== 'sessionStorage') return null;

    const storage = persistenceType === 'localStorage' ? localStorage : sessionStorage;
    const savedMap = storage.getItem(`table-columns-${tableKey}`);
    if (!savedMap) return null;

    try {
      return JSON.parse(savedMap);
    } catch (error) {
      console.error('Parse saved columns map error:', error);
      return null;
    }
  };

  // 从 API 获取列配置
  const fetchColumnsMapFromApi = async () => {
    if (!tableKey || !columnsState?.api?.fetchPath) return null;

    try {
      setIsLoading(true);
      // 传入 tableKey 作为 configType
      const settings = await fetchColumnSettings(columnsState.api, tableKey);

      // 查找匹配的配置
      const setting = settings.find(s => s.configType === tableKey);
      if (setting && setting.configParams) {
        return setting.configParams;
      }

      // 如果没有找到配置，使用默认配置并上传到服务器
      console.log('No column settings found for this table, using default settings');
      const defaultSettings = getDefaultColumnsMap();

      // 将默认配置上传到服务器
      if (defaultSettings && columnsState?.api?.savePath) {
        try {
          const data: ColumnSettingData = {
            configType: tableKey,
            configParams: defaultSettings
          };

          await saveColumnSetting(columnsState.api, data);
          console.log('Default column settings uploaded to server');
        } catch (saveError) {
          console.error('Failed to upload default column settings to server:', saveError);
        }
      }

      return defaultSettings;
    } catch (error) {
      console.error('Failed to fetch column settings from API:', error);
      // 如果 API 请求失败，使用默认配置
      return getDefaultColumnsMap();
    } finally {
      setIsLoading(false);
    }
  };

  // 保存列配置到 API
  const saveColumnsMapToApi = async (newMap: Record<string, ColumnState>) => {
    if (!tableKey || !columnsState?.api?.savePath) return false;

    try {
      const data: ColumnSettingData = {
        configType: tableKey,
        configParams: newMap
      };

      return await saveColumnSetting(
        columnsState.api,
        data
      );
    } catch (error) {
      console.error('Failed to save column settings to API:', error);
      return false;
    }
  };

  // 保存列配置到本地存储
  const saveColumnsMapToStorage = (newMap: Record<string, ColumnState>) => {
    if (!tableKey) return;

    const persistenceType = columnsState?.persistenceType;
    if (persistenceType !== 'localStorage' && persistenceType !== 'sessionStorage') return;

    const storage = persistenceType === 'localStorage' ? localStorage : sessionStorage;
    storage.setItem(`table-columns-${tableKey}`, JSON.stringify(newMap));
  };

  // 列显示状态
  const [columnsMap, setColumnsMap] = useState<Record<string, ColumnState>>(getDefaultColumnsMap());

  // 初始化列配置
  useEffect(() => {
    const initColumnSettings = async () => {
      // 如果没有提供持久化类型，使用默认配置
      if (!columnsState?.persistenceType) return;

      let settings: Record<string, ColumnState> | null = null;

      // 根据持久化类型获取配置
      if (columnsState.persistenceType === 'api') {
        settings = await fetchColumnsMapFromApi();
      } else {
        settings = getColumnsMapFromStorage();
      }

      // 如果获取到配置，更新状态
      if (settings) {
        setColumnsMap(settings);
      }
    };

    initColumnSettings();
  }, [tableKey, columnsState?.persistenceType]);

  // 处理列显示状态变化
  const handleColumnsMapChange = async (newMap: Record<string, ColumnState>) => {
    setColumnsMap(newMap);
    columnsState?.onChange?.(newMap);

    // 如果提供了持久化类型，保存配置
    if (tableKey && columnsState?.persistenceType) {
      if (columnsState.persistenceType === 'api') {
        await saveColumnsMapToApi(newMap);
      } else {
        saveColumnsMapToStorage(newMap);
      }
    }
  };

  // /* 计算搜索框高度，以使Table高度自适应屏幕高度 */
  const tableSize = () => {
    if (searchRef && searchRef.current) {
      const height = `calc(80vh - ${(searchRef.current.clientHeight || 0) + 100}px)`;
      return height;
    }
    return "65vh";
  };

  const crossPageSelectRowSelection = {
    selectedRowKeys: selectedRowKeys,
    onSelect: (record: any, selected: boolean) => {
      let keys: any[] = [];
      let rows: any[] = [];
      if (selected) {
        keys = selectedRowKeys.concat([record[rowKey]]);
        rows = selectedRows.concat([record]);
      } else {
        keys = selectedRowKeys.filter((v) => v !== record[rowKey]);
        rows = selectedRows.filter((v) => v[rowKey] !== record[rowKey]);
      }
      setSelectedRowKeys(keys);
      setSelectedRows(rows);
      crossPageSelect &&
        crossPageSelect(keys, rows, () => {
          setSelectedRowKeys([]);
          setSelectedRows([]);
        });
    },
    onSelectAll: (
      selected: boolean,
      _selectedRowsInfo: any,
      changeRows: any
    ) => {
      let keys: any[] = [];
      let rows: any[] = [];
      if (selected) {
        const set1 = new Set(selectedRowKeys);
        const set2 = new Set(selectedRows);
        changeRows.forEach((v: any) => {
          set1.add(v[rowKey]);
          set2.add(v);
        });
        keys = [...set1];
        rows = [...set2];
      } else {
        const arr1 = selectedRows.filter((v) => {
          return !changeRows.some((i: any) => i[rowKey] === v[rowKey]);
        });
        const arr2 = selectedRowKeys.filter((v) => {
          return !changeRows.some((i: any) => i[rowKey] === v);
        });

        keys = [...arr2];
        rows = [...arr1];
      }
      setSelectedRowKeys(keys);
      setSelectedRows(rows);
      crossPageSelect &&
        crossPageSelect(keys, rows, () => {
          setSelectedRowKeys([]);
          setSelectedRows([]);
        });
    },
  };

  // 根据列显示状态过滤、排序和固定列
  const filteredColumns = useMemo(() => {
    // 创建列和对应key的映射
    const columnsWithKey = columns.map((column, index) => {
      const key = genColumnKey(column.key || column.dataIndex, index);
      return { column, key };
    });

    // 先根据显示状态过滤
    const filtered = columnsWithKey.filter(({ key }) => {
      return !key || columnsMap[key]?.show !== false;
    });

    // 根据 order 属性排序
    const sorted = [...filtered].sort((a, b) => {
      const orderA = columnsMap[a.key]?.order || 0;
      const orderB = columnsMap[b.key]?.order || 0;
      return orderA - orderB;
    });

    // 应用固定列设置
    const withFixed = sorted.map(({ column, key }) => {
      const config = columnsMap[key];
      if (config && config.fixed) {
        return { ...column, fixed: config.fixed, key };
      }
      return { ...column, key };
    });

    // 根据固定状态进行最终排序：左固定 -> 不固定 -> 右固定
    return withFixed.sort((a, b) => {
      // 先按固定状态排序
      const fixedA = a.fixed === 'left' ? -1 : a.fixed === 'right' ? 1 : 0;
      const fixedB = b.fixed === 'left' ? -1 : b.fixed === 'right' ? 1 : 0;

      if (fixedA !== fixedB) {
        return fixedA - fixedB;
      }

      // 如果固定状态相同，则按order排序
      const orderA = columnsMap[a.key]?.order || 0;
      const orderB = columnsMap[b.key]?.order || 0;
      return orderA - orderB;
    });
  }, [columns, columnsMap]);

  return (
    <div
      className={`x-coreui-common-table ${className || ""} ${
        theme == "dark" ? "dark-theme" : ""
      }`}
    >
      <div className="middle-btn">
        {middleBtns &&
          middleBtns.map((item: any) => {
            return (
              <React.Fragment key={item.title}>
                {item?.enablePopConfirm ? (
                  <Popconfirm
                    title={item.popConfirmContent}
                    onConfirm={item.onClick}
                    key={item.key}
                  >
                    <Button
                      type="primary"
                      loading={item.loading}
                      clstag={item.clstag}
                    >
                      {item.title}
                    </Button>
                  </Popconfirm>
                ) : (
                  <Button
                    type={item.btnType ?? "primary"}
                    key={item.key}
                    clstag={item.clstag}
                    loading={item.loading}
                    onClick={() => item.onClick()}
                  >
                    {item.title}
                  </Button>
                )}
              </React.Fragment>
            );
          })}
        {showColumnSetting && (
          <ColumnSetting
            columns={columns}
            columnsMap={columnsMap}
            onColumnsMapChange={handleColumnsMapChange}
            tableKey={tableKey}
            defaultColumnsMap={defaultColumnsState}
            persistenceType={columnsState?.persistenceType}
            onResetViaApi={async (tableKey, defaultMap) => {
              if (columnsState?.persistenceType === 'api' && columnsState?.api?.savePath) {
                try {
                  const data: ColumnSettingData = {
                    configType: tableKey,
                    configParams: defaultMap
                  };

                  await saveColumnSetting(
                    columnsState.api,
                    data
                  );
                } catch (error) {
                  console.error('Failed to reset column settings via API:', error);
                }
              }
            }}
            {...columnSettingProps}
          />
        )}
      </div>
      <ConfigProvider5 prefixCls="x-coreui">
        {isLoading ? (
          <div className="column-settings-loading">
            <Spin tip="加载列配置..." />
          </div>
        ) : (
          <BaseTable
            key={tableKey}
            locale={customLocale}
            rowClassName={rowClassName && rowClassName}
            columns={filteredColumns}
            loading={loading}
            dataSource={tableListData?.list as any[] ?? []}
            rowKey={(record: any) => record[rowKey]}
            expandable={expandable}
            summary={summary}
            scroll={{
              y: scrollY ?? tableSize(),
            }}
            rowSelection={
              crossPageSelect
                ? crossPageSelectRowSelection
                : rowSelection
                ? rowSelection
                : undefined
            }
            pagination={
              notPage
                ? false
                : {
                    position: ["bottomRight"],
                    total: tableListData?.totalNumber,
                    current: searchCondition?.pageNum,
                    pageSize: searchCondition?.pageSize,
                    showQuickJumper: true,
                    showSizeChanger: true,
                    pageSizeOptions:
                      props.pageSizeOptions ?? defaultPageSizeOptions,
                    showTotal: (total: number) => `共${total}条记录`,
                    locale: customLocale.pagination,
                  }
            }
            onChange={(paginationData: any, _filters: any, _sorter: any, extra: any) => {
              if (extra.action === "paginate") {
                const { current, pageSize } = paginationData;
                const newSearchValue = {
                  ...searchCondition,
                  pageNum: current,
                  pageSize: pageSize,
                };
                onPageChange && onPageChange(newSearchValue);
              }
            }}
          />
        )}


      </ConfigProvider5>
    </div>
  );
}

export default React.memo(CommonTable);
