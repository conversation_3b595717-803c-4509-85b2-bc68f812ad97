import * as XLSX from 'xlsx';
import { ColumnConfig } from './types';

export class ErrorExporter {
  constructor(private columnConfig: ColumnConfig[]) {}

  generateErrorSheet(invalidData: any[]): XLSX.WorkBook {
    const headers = [...this.columnConfig.map(col => col.title), '错误信息'];
    
    const data = invalidData.map(row => {
      const rowData = this.columnConfig.map(col => row[col.key]);
      return [...rowData, row.errors.join('; ')];
    });

    const ws = XLSX.utils.aoa_to_sheet([headers, ...data]);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, '错误数据');
    
    return wb;
  }

  downloadErrorFile(workbook: XLSX.WorkBook) {
    XLSX.writeFile(workbook, `错误数据_${new Date().getTime()}.xlsx`);
  }
}
