import React from "react";
import PropTypes from "prop-types";
import "./index.scss";
export declare const PickerGroup: {
    ({ theme, maxSize, maxTagCount, pickerLabel, defaultDate, btnList, onChange, }: {
        theme: any;
        maxSize: any;
        maxTagCount: any;
        pickerLabel: any;
        defaultDate: any;
        btnList: any;
        onChange: any;
    }): React.JSX.Element;
    propTypes: {
        /**
         * 主题色
         */
        theme: PropTypes.Requireable<string>;
        /**
         * 最多可选择几项
         */
        maxSize: PropTypes.Validator<number>;
        /**
         * 最多显示多少个tag
         */
        maxTagCount: PropTypes.Requireable<number>;
        onChange: PropTypes.Requireable<(...args: any[]) => any>;
        defaultDate: PropTypes.Requireable<Date>;
        btnList: PropTypes.Requireable<(PropTypes.InferProps<{
            label: PropTypes.Requireable<string>;
            value: PropTypes.Requireable<string>;
            isActive: PropTypes.Requireable<boolean>;
        }> | null | undefined)[]>;
        pickerLabel: PropTypes.Requireable<string>;
    };
    defaultProps: {
        theme: string;
        maxSize: number;
        maxTagCount: number;
        btnList: null;
        defaultDate: null;
        pickerLabel: null;
    };
};
