import React from "react";
import PropTypes from "prop-types";
import DatepickerGroup from "@components/DatePickerGroup";
import dayjs from "dayjs";
import "./index.scss";

export const PickerGroup = ({
  theme,
  maxSize,
  maxTagCount,
  pickerLabel,
  defaultDate,
  btnList,
  onChange,
}) => {
  return (
    <div className="picker-container">
      <DatepickerGroup
        theme={theme}
        maxSize={maxSize}
        maxTagCount={maxTagCount}
        pickerLabel={pickerLabel}
        defaultDate={defaultDate}
        onChange={onChange}
        btnList={btnList}
      />
    </div>
  );
};

PickerGroup.propTypes = {
  /**
   * 主题色
   */
  theme: PropTypes.oneOf(["dark", "light"]),
  /**
   * 最多可选择几项
   */
  maxSize: PropTypes.number.isRequired,
  /**
   * 最多显示多少个tag
   */
  maxTagCount: PropTypes.number,
  onChange: PropTypes.func,
  defaultDate: PropTypes.instanceOf(Date),
  btnList: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string,
      value: PropTypes.oneOf(["DAY", "WEEK", "MONTH"]),
      isActive: PropTypes.bool,
    })
  ),
  pickerLabel: PropTypes.string,
};

PickerGroup.defaultProps = {
  theme: "dark",
  maxSize: 7,
  maxTagCount: 4,
  btnList: null,
  defaultDate: null,
  pickerLabel: null,
};
