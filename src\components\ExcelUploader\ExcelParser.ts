import * as XLSX from 'xlsx';
import { ColumnConfig, ParseResult, ValidationResult } from './types';
import { ValidatorManager } from './ValidatorManager';

export class ExcelParser {
  private validatorManager: ValidatorManager;

  constructor(
    private columnConfig: ColumnConfig[],
    customValidators?: Record<string, (value: any, config: ColumnConfig) => ValidationResult>
  ) {
    this.validatorManager = new ValidatorManager();
    if (customValidators) {
      Object.entries(customValidators).forEach(([name, validator]) => {
        this.validatorManager.registerValidator(name, validator);
      });
    }
  }

  async parse(file: File): Promise<ParseResult> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: 'array' });
          const worksheet = workbook.Sheets[workbook.SheetNames[0]];
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as any[][];
          
          const headers = jsonData[0] as string[];
          const rows = jsonData.slice(1).filter(row => {
            // 过滤掉完全为空的行
            return row.some(cell => cell !== undefined && cell !== null && cell !== '');
          });
          
          resolve({ headers, data: rows });
        } catch (error) {
          reject(new Error('Excel文件解析失败'));
        }
      };

      reader.onerror = () => {
        reject(new Error('文件读取失败'));
      };

      reader.readAsArrayBuffer(file);
    });
  }

  validateRow(row: any): ValidationResult {
    // 检查是否所有列都为空
    const isEmptyRow = this.columnConfig.every(config => {
      const value = row[config.key];
      return value === undefined || value === null || value === '';
    });

    if (isEmptyRow) {
      return { valid: false, errors: [], isEmpty: true };
    }

    const errors: string[] = [];
    let isValid = true;

    this.columnConfig.forEach((config) => {
      const value = row[config.key];
      const result = this.validatorManager.validate(value, config);
      
      if (!result.valid) {
        errors.push(`${config.title}: ${result.errors.join(', ')}`);
        isValid = false;
      }
    });

    return { valid: isValid, errors, isEmpty: false };
  }

  separateData(data: any[]): { validData: any[]; invalidData: any[] } {
    const validData: any[] = [];
    const invalidData: any[] = [];

    data.forEach((row) => {
      const result = this.validateRow(row);
      if (result.valid) {
        validData.push(row);
      } else if (!result.isEmpty) {
        // 只有非空行且校验失败的数据才加入到invalidData中
        invalidData.push({ ...row, errors: result.errors });
      }
    });

    return { validData, invalidData };
  }
}
