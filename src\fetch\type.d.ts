declare enum Method {
  POST = "POST",
  GET = "GET",
  PUT = "PUT",
  DELETE = "DELETE",
}
declare interface RequestOptions {
  method?: Method | string;
  path?: string;
  absoluteURL?: string;
  contentType?:
    | "application/json"
    | "application/x-www-form-urlencoded"
    | "text/plain"
    | "multipart/form-data";
  urlParams?: {
    [key: string]: string | number;
  };
  body?: {
    [key: string]: any;
  };
  headers?: {
    [key: string]: string;
  };
  timeout?: number;
  useMock?: boolean;
  mockData?: any;
  newGeteway?: boolean;
}

declare interface HTTPFetchActionParams extends RequestOptions {
  nextActionFunc?: AnyFunc;
  actionType: string;
}

declare interface HTTPResponse {
  code: string | number;
  data: any;
  message?: string;
  errorCode?: number;
}
