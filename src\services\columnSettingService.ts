import { request } from '../fetch';
import { ColumnState } from '../components/CommonTable/ColumnSetting';

// 定义 RequestOptions 类型（从全局类型中复制）
interface RequestOptions {
  method?: string;
  path?: string;
  absoluteURL?: string;
  contentType?:
    | "application/json"
    | "application/x-www-form-urlencoded"
    | "text/plain"
    | "multipart/form-data";
  urlParams?: {
    [key: string]: string | number;
  };
  body?: {
    [key: string]: any;
  };
  headers?: {
    [key: string]: string;
  };
  timeout?: number;
  useMock?: boolean;
  mockData?: any;
  newGeteway?: boolean;
}

// 列配置数据类型
export interface ColumnSettingData {
  configType: string; // 配置类型，如表格的唯一标识
  configParams: Record<string, ColumnState>; // 列配置参数
}

// 列配置服务配置选项
export interface ColumnSettingServiceOptions {
  // API 路径
  fetchPath?: string;
  savePath?: string;
  // 是否使用绝对路径
  useAbsoluteURL?: boolean;
  // LOPDN 参数
  LOPDN?: string;
  // 超时时间
  timeout?: number;
}

/**
 * 获取列配置
 * @param options 服务配置选项
 * @returns 列配置数组
 */
export const fetchColumnSettings = async (
  options: ColumnSettingServiceOptions,
  configType: string
): Promise<ColumnSettingData[]> => {
  try {
    const { fetchPath, useAbsoluteURL, LOPDN, timeout } = options;

    if (!fetchPath) {
      console.error('fetchPath is required for fetchColumnSettings');
      return [];
    }

    const requestOptions: RequestOptions = {
      method: 'POST',
      ...(useAbsoluteURL ? { absoluteURL: fetchPath } : { path: fetchPath }),
      headers: LOPDN ? { 'LOP-DN': LOPDN } : undefined,
      body: configType ? { configTypeList: [configType] } : undefined,
      timeout: timeout || 15000
    };

    const response: any = await request(requestOptions);
    return response.data || [];
  } catch (error) {
    console.error('Failed to fetch column settings:', error);
    return [];
  }
};

/**
 * 保存列配置
 * @param options 服务配置选项
 * @param data 列配置数据
 * @returns 保存结果
 */
export const saveColumnSetting = async (
  options: ColumnSettingServiceOptions,
  data: ColumnSettingData
): Promise<boolean> => {
  try {
    const { savePath, useAbsoluteURL,  LOPDN, timeout } = options;

    if (!savePath) {
      console.error('savePath is required for saveColumnSetting');
      return false;
    }

    const requestOptions: RequestOptions = {
      method: 'POST',
      ...(useAbsoluteURL ? { absoluteURL: savePath } : { path: savePath }),
      body: data,
      headers: LOPDN ? { 'LOP-DN': LOPDN } : undefined,
      timeout: timeout || 15000
    };

    await request(requestOptions);
    return true;
  } catch (error) {
    console.error('Failed to save column setting:', error);
    return false;
  }
};

