import type { Meta, StoryObj } from "@storybook/react";
import { GeoLocationSelectExample } from "./GeoLocationSelectExample";
import React from "react";

const meta: Meta<typeof GeoLocationSelectExample> = {
  title: "GeoLocationSelect",
  component: GeoLocationSelectExample,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof GeoLocationSelectExample>;

export const Default: Story = {
  args: {
    dockPoints: [
      { id: '1', name: '上海港', address: '上海市宝山区' },
      { id: '2', name: '深圳港', address: '深圳市南山区' },
      { id: '3', name: '青岛港', address: '山东省青岛市黄岛区' },
      { id: '4', name: '天津港', address: '天津市滨海新区' },
      { id: '5', name: '宁波港', address: '浙江省宁波市北仑区' },
      { id: '6', name: '广州港', address: '广东省广州市南沙区' },
    ],
    defaultActiveTab: 'dockPoint',
    placeholder: '请输入地点名称搜索',
  },
};

export const LocationTabActive: Story = {
  args: {
    dockPoints: [
      { id: '1', name: '上海港', address: '上海市宝山区' },
      { id: '2', name: '深圳港', address: '深圳市南山区' },
      { id: '3', name: '青岛港', address: '山东省青岛市黄岛区' },
      { id: '4', name: '天津港', address: '天津市滨海新区' },
      { id: '5', name: '宁波港', address: '浙江省宁波市北仑区' },
      { id: '6', name: '广州港', address: '广东省广州市南沙区' },
    ],
    defaultActiveTab: 'location',
    placeholder: '请输入地点名称搜索',
  },
};

export const CustomOptionRender: Story = {
  args: {
    dockPoints: [
      { id: '1', name: '上海港', address: '上海市宝山区' },
      { id: '2', name: '深圳港', address: '深圳市南山区' },
      { id: '3', name: '青岛港', address: '山东省青岛市黄岛区' },
      { id: '4', name: '天津港', address: '天津市滨海新区' },
      { id: '5', name: '宁波港', address: '浙江省宁波市北仑区' },
      { id: '6', name: '广州港', address: '广东省广州市南沙区' },
    ],
    defaultActiveTab: 'dockPoint',
    placeholder: '请输入地点名称搜索',
    useCustomRender: true,
  },
};

export const DockPointsWithoutAddress: Story = {
  args: {
    dockPoints: [
      { id: '1', name: '上海港' },
      { id: '2', name: '深圳港' },
      { id: '3', name: '青岛港' },
      { id: '4', name: '天津港' },
      { id: '5', name: '宁波港' },
      { id: '6', name: '广州港' },
    ],
    defaultActiveTab: 'dockPoint',
    placeholder: '请输入地点名称搜索',
  },
};
