import { HttpStatusCode } from "./constant";
import { buildURL, buildFetchInit, parseResponseData } from "./util";
import { message } from "antd";
/**
 * @param {RequestOptions} param 接收参数
 * @param {string} [param.path] 请求地址
 * @param {boolean} [autoTip=true] 是否自动提示
 * @return {Promise}
 */

export const doRequest = async (param: RequestOptions, autoTip = true) => {
  return new Promise((resolve, reject) => {
    if (param.useMock) {
      resolve({
        code: HttpStatusCode.Success,
        data: param.mockData,
      });
    } else {
      const url = buildURL(param);
      const fetchInit = buildFetchInit(param);
      window
        .fetch(url, fetchInit)
        .then(async (res) => {
          const httpCode = res.status;
          const respBody = await parseResponseData(
            res,
            res.headers.get("Content-Type")
          );
          // 处理服务端统一封装的状态码，接口请求成功了，但是code可能不是200
          if (httpCode < 200 || httpCode >= 300) {
            if (autoTip) {
              message.error(respBody.message || "数据请求失败，请重试！");
            }
            reject({
              httpCode: httpCode,
              code: respBody.code,
              message: respBody.message || "数据请求失败，请重试！",
            });
          } else if (
            typeof respBody === "object" &&
            respBody.code !== HttpStatusCode.Success
          ) {
            if (autoTip) {
              message.error(respBody.message || "数据请求失败，请重试！");
            }
            reject({
              httpCode: httpCode,
              code: respBody.code,
              message: respBody.message || "数据请求失败，请重试！",
            });
          } else {
            resolve({
              httpCode: httpCode,
              code: respBody.code || HttpStatusCode.Success,
              data: respBody.data,
              message: respBody.message,
            });
          }
        })
        .catch((err) => {
          if (autoTip) {
            message.error((err && err.message) || "网络错误");
          }
          reject({
            code: (err && err.code) || HttpStatusCode.ServiceException,
            message: (err && err.message) || "网络错误",
          });
        });
    }
  });
};

const defaultRequestOption: RequestOptions = {
  method: "GET",
  contentType: "application/json",
  timeout: 15000,
};

function makeTimeoutPromise(timeout: number) {
  return new Promise((resolve: any, reject: any) => {
    setTimeout(() => {
      resolve({
        code: "3001",
        message: "请求超时",
        requestId: "",
        eventType: "",
        traceId: "",
      });
    }, timeout);
  });
}

/**
 *
 * @param {RequestOptions} opts 请求参数
 * @return {Promise}
 */
export async function request(
  opts: RequestOptions,
  autoTip?: boolean,
  overtime?: number
) {
  const options = Object.assign({}, defaultRequestOption, opts);
  const time = overtime ? overtime : options.timeout;
  return Promise.race([makeTimeoutPromise(time!), doRequest(options, autoTip)]);
}
