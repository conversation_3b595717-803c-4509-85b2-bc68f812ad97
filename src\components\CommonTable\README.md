# CommonTable 组件

CommonTable 是一个基于 Ant Design Table 的增强表格组件，提供了更多实用功能。

## 新增功能：可配置列

现在 CommonTable 支持列的显示/隐藏配置，类似于 Ant Design ProComponents 的 ProTable。

### 基本用法

```tsx
import React, { useState } from 'react';
import { CommonTable, createDefaultColumnsState } from '@/components';

const MyTable = () => {
  const columns = [
    { title: '姓名', dataIndex: 'name', key: 'name' },
    { title: '年龄', dataIndex: 'age', key: 'age' },
    { title: '地址', dataIndex: 'address', key: 'address' },
    { title: '电话', dataIndex: 'phone', key: 'phone' },
    { title: '邮箱', dataIndex: 'email', key: 'email' },
  ];
  
  // 默认隐藏的列
  const defaultHiddenColumns = ['phone', 'email'];
  
  // 列显示状态
  const [columnsState, setColumnsState] = useState(
    createDefaultColumnsState(columns, defaultHiddenColumns)
  );
  
  return (
    <CommonTable
      tableListData={{ list: dataSource, totalNumber: dataSource.length }}
      columns={columns}
      loading={false}
      rowKey="id"
      tableKey="my-table" // 用于持久化存储列设置的唯一标识
      showColumnSetting={true} // 显示列设置按钮
      columnsState={{
        value: columnsState, // 列显示状态
        onChange: setColumnsState, // 列显示状态变化回调
        persistenceType: 'localStorage', // 持久化类型，可选 'localStorage' 或 'sessionStorage'
      }}
      defaultColumnsState={createDefaultColumnsState(columns, defaultHiddenColumns)} // 默认列设置
    />
  );
};
```

### 新增属性

| 属性 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| showColumnSetting | 是否显示列设置按钮 | boolean | false |
| columnsState | 列显示状态配置 | object | - |
| columnsState.value | 列显示状态 | Record<string, { show: boolean }> | - |
| columnsState.onChange | 列显示状态变化回调 | (map: Record<string, { show: boolean }>) => void | - |
| columnsState.persistenceType | 持久化类型 | 'localStorage' \| 'sessionStorage' | - |
| defaultColumnsState | 默认列设置 | Record<string, { show: boolean }> | - |
| tableKey | 表格唯一标识，用于持久化存储列设置 | string | - |

### 工具函数

#### createDefaultColumnsState

创建默认的列显示状态配置。

```tsx
import { createDefaultColumnsState } from '@/components';

// 创建默认列设置，隐藏 'phone' 和 'email' 列
const defaultState = createDefaultColumnsState(columns, ['phone', 'email']);
```

#### filterColumnsByState

根据列显示状态过滤列。

```tsx
import { filterColumnsByState } from '@/components';

// 根据列显示状态过滤列
const visibleColumns = filterColumnsByState(columns, columnsState);
```
