import PropTypes from "prop-types";
import type { Meta } from "@storybook/react";
import type { StoryObj } from "@storybook/react";
import { DateType } from "@components/DatePicker/type";
import "./index.scss";
import { PickerGroup } from "./PickerGroup";
import dayjs from "dayjs";

const meta: Meta<typeof PickerGroup> = {
  title: "DatepickerGroup",
  tags: ["autodocs"],
  component: PickerGroup,
};

export default meta;
type Story = StoryObj<typeof PickerGroup>;

export const DarkTheme: Story = {
  args: {
    theme: "dark",
    maxSize: 5,
    maxTagCount: 3,
    pickerLabel: {
      [DateType.DAY]: "每日单量",
      [DateType.WEEK]: "每周单量",
      [DateType.MONTH]: "每月单量",
    },
    btnList: [
      {
        label: "日",
        value: DateType.DAY,
        isActive: true,
      },
      {
        label: "周",
        value: DateType.WEEK,
        isActive: false,
      },
      {
        label: "月",
        value: DateType.MONTH,
        isActive: false,
      },
    ],
    defaultDate: dayjs(),
    onChange: function(value:any){console.log(11111111)}
  },
};
