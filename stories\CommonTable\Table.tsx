import React, { useState } from "react";
import PropTypes from "prop-types";
import CTable from "@components/CommonTable";
import { createDefaultColumnsState } from "@components/CommonTable/columnUtils";
import { Table } from "antd";

export const CommonTable: React.FC<any> = (props) => {
  const renderSummary = (pageData: any) => {
    return (
      <>
        <Table.Summary.Row>
          <Table.Summary.Cell index={0}>Total</Table.Summary.Cell>
          <Table.Summary.Cell index={1}></Table.Summary.Cell>
          <Table.Summary.Cell index={2}></Table.Summary.Cell>
        </Table.Summary.Row>
      </>
    );
  };
  return (
    <div className={`table-container`}>
      <CTable {...props} summary={renderSummary} />
    </div>
  );
};

CommonTable.propTypes = {
  tableListData: PropTypes.shape({
    list: PropTypes.arrayOf(PropTypes.object).isRequired,
    totalNumber: PropTypes.number,
    totalPage: PropTypes.number,
  }).isRequired,
  columns: PropTypes.array.isRequired,
  loading: PropTypes.bool.isRequired,
  rowKey: PropTypes.string.isRequired,
  middleBtns: PropTypes.arrayOf(
    PropTypes.shape({
      title: PropTypes.string.isRequired,
      sourceCode: PropTypes.string.isRequired,
      btnType: PropTypes.string.isRequired,
      onClick: PropTypes.func.isRequired,
      enablePopConfirm: PropTypes.bool,
      popConfirmContent: PropTypes.string,
    })
  ),
  searchRef: PropTypes.any,
  searchCondition: PropTypes.any,
  onPageChange: PropTypes.func,
  expandable: PropTypes.any,
  rowSelection: PropTypes.object,
  notPage: PropTypes.bool,
  scrollY: PropTypes.number,
  tableKey: PropTypes.string,
  // 列设置相关属性
  showColumnSetting: PropTypes.bool,
  columnsState: PropTypes.shape({
    value: PropTypes.object,
    onChange: PropTypes.func,
    persistenceType: PropTypes.oneOf(['localStorage', 'sessionStorage']),
  }),
  defaultColumnsState: PropTypes.object,
};

CommonTable.defaultProps = {
  tableListData: {
    list: [],
    totalNumber: 0,
    totalPage: 0,
  },
  columns: [
    {
      title: "序号",
      dataIndex: "order",
      align: "center",
      width: 70,
      render: (text: any, record: any, index: number) => index + 1,
    },
    {
      title: "设备分组",
      dataIndex: "groupName",
      align: "center",
      ellipsis: true,
    },
    {
      title: "型号",
      dataIndex: "productModelNo",
      align: "center",
      ellipsis: true,
    },
    {
      title: "分组",
      dataIndex: "groupName",
      align: "center",
      ellipsis: true,
    },
    {
      title: "SN",
      dataIndex: "deviceName",
      align: "center",
      ellipsis: true,
    },
    {
      title: "电量",
      dataIndex: "deviceRestElectric",
      align: "center",
      ellipsis: true,
    },
  ],
  loading: false,
  rowKey: "id",
};
