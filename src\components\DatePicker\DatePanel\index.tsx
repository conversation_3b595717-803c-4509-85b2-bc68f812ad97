import React, { useEffect, useState } from "react";
import weekOfYear from "dayjs/plugin/weekOfYear";
import dayjs, { Dayjs } from "dayjs";
import MonthHeader from "./MonthHeader";
import DateCell from "./DateCell";
import WeekHeader from "./WeekHeader";
import { DateType } from "../type";
import isoWeeksInYear from "dayjs/plugin/isoWeeksInYear";
import isLeapYear from "dayjs/plugin/isLeapYear";
import { formatWeekStartDate, formatWeekEndofDate } from "@utils/index";
import { AnyFunc } from "../../../global";
dayjs.extend(isoWeeksInYear);
dayjs.extend(isLeapYear);
dayjs.extend(weekOfYear);
export interface DatePanelProps {
  type: DateType;
  currentDate: Dayjs;
  defaultDate: Dayjs;
  changeSelect: AnyFunc;
  changeDate: AnyFunc;
  selectedList: { value: string | number }[];
  maxSize: number;
  visible: boolean;
}

const DatePanel: React.FC<DatePanelProps> = (props) => {
  const {
    type,
    currentDate,
    changeSelect,
    selectedList,
    maxSize,
    changeDate,
    defaultDate,
    visible,
  } = props;

  const getMonthCell = () => {
    const _monthCell: any = [];
    for (let i = 1; i < 13; i++) {
      _monthCell.push({
        value: i,
        text: i + "月",
        year: currentDate.year(),
      });
    }

    return _monthCell;
  };
  const getWeekList = () => {
    const fullWeeks: any[] = [];
    const fullYearWeeks = currentDate.isoWeeksInYear();
    const startWeekOfMonth = currentDate.startOf("month").week();
    let endWeekOfMonth = currentDate.endOf("month").week();
    // 最后一周有可能是下一年的第一周
    endWeekOfMonth = endWeekOfMonth === 1 ? fullYearWeeks : endWeekOfMonth;
    const delt = endWeekOfMonth - startWeekOfMonth;
    for (let i = 0; i < delt + 1; i++) {
      const startDate = formatWeekStartDate(startWeekOfMonth + i);
      const endDate = formatWeekEndofDate(startWeekOfMonth + i);
      fullWeeks.push({
        value: startWeekOfMonth + i,
        text: `第${startWeekOfMonth + i}周`,
        year: currentDate.year(),
        subText: `${startDate}-${endDate}`,
      });
    }
    return fullWeeks;
  };

  return (
    <div
      className="date-panel-container"
      style={{ display: visible ? "block" : "none" }}
    >
      {type === DateType.MONTH && (
        <MonthHeader currentDate={currentDate} changeDate={changeDate} />
      )}
      {type === DateType.WEEK && (
        <WeekHeader currentDate={currentDate} changeDate={changeDate} />
      )}
      <div className="panel-modal">
        <DateCell
          list={type === DateType.MONTH ? getMonthCell() : getWeekList()}
          type={type}
          defaultDate={defaultDate}
          selectedList={selectedList}
          maxSize={maxSize}
          onSelect={(
            selectedList: { value: string | number; text: string }[]
          ) => {
            if (selectedList.length > maxSize) {
              return;
            }
            changeSelect(selectedList);
          }}
        />
      </div>
    </div>
  );
};

export default DatePanel;
