import React from 'react';
import { AnyFunc } from '../../../global';

export interface DateInputProps {
  placeholder?: string;
  onChange?: (id: string) => void;
  selectedList?: { value: string | number; text: string }[];
  maxTagCount: number;
  onDelete: (id: string | number) => void;
  onClick: AnyFunc;
  onClear: AnyFunc;
}

const DateInput: React.FC<DateInputProps> = (props) => {
  const {
    placeholder,
    onChange,
    selectedList,
    onDelete,
    maxTagCount,
    onClick,
    onClear,
  } = props;
  return (
    <div className="date-label" onClick={onClick}>
      <i className="icon">
        <svg
          viewBox="64 64 896 896"
          focusable="false"
          data-icon="calendar"
          width="1em"
          height="1em"
          fill="currentColor"
          aria-hidden="true"
        >
          <path d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"></path>
        </svg>
      </i>
      <div className="date-label_content">
        {selectedList?.length === 0 && placeholder}
        {selectedList
          ?.slice(0, maxTagCount)
          ?.map((item: { value: string | number; text: string }) => (
            <span className="date-select-item" key={item.value}>
              <span className="date-select-item_context">{item.text}</span>
              <span
                className="date-select-item_remove"
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  onDelete(item.value);
                }}
              >
                <svg
                  width="8"
                  height="8"
                  viewBox="0 0 8 8"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M1 1l5.657 5.657M1 6.657L6.657 1"
                    fillRule="nonzero"
                    stroke="#666"
                    strokeWidth="1.2"
                    fill="none"
                    opacity=".85"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </span>
            </span>
          ))}
        {selectedList && selectedList?.length > maxTagCount && (
          <span className="date-select-item" key="more-tag">
            <span className="date-select-item_context">
              +{selectedList?.length - maxTagCount}...
            </span>
          </span>
        )}
      </div>
      <i className="clear" onClick={onClear}>
        <svg
          width="8"
          height="8"
          viewBox="0 0 8 8"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M1 1l5.657 5.657M1 6.657L6.657 1"
            fillRule="nonzero"
            stroke="#666"
            strokeWidth="1.2"
            fill="none"
            opacity=".85"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </i>
    </div>
  );
};

export default DateInput;
