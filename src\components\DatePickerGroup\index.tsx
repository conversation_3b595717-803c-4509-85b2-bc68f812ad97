import React, { useState } from "react";
import "./index.scss";
import dayjs, { Dayjs } from "dayjs";
import locale from "antd/es/date-picker/locale/zh_CN";
import { ConfigProvider, DatePicker } from "antd";
import zhCN from "antd/locale/zh_CN";
import "dayjs/locale/zh-cn";
import DatePickerCmp from "@components/DatePicker";
import { DateType } from "../DatePicker/type";
import ButtonRadio from "@components/ButtonRadio";
import classNames from "classnames";
import { AnyFunc } from "../../global";
const { RangePicker } = DatePicker;
export interface DatepickerGroupProps {
  /**
   * 主题
   */
  theme?: "dark" | "light";
  /**
   * 最多可选择几项
   */
  maxSize?: number;
  /**
   * 最多显示多少个tag
   */
  maxTagCount?: number;
  /**
   * 日期类型切换btnList
   */
  btnList?: {
    label: string;
    isActive: boolean;
    value: DateType;
  }[];
  pickerLabel: {
    [key in DateType]?: string;
  };
  defaultDate: Dayjs;
  // disabledDate?: (currentDate: Dayjs) => boolean;
  onChange: AnyFunc;
}
const DatepickerGroup: React.FC<DatepickerGroupProps> = (props) => {
  const {
    btnList,
    onChange,
    defaultDate,
    pickerLabel,
    maxSize,
    maxTagCount,
    theme = "dark",
  } = props;
  const [dateType, setDateType] = useState<DateType>(DateType.DAY);
  const [dates, setDates] = useState<any>([defaultDate, defaultDate]);

  const disabledDate = (current: Dayjs) => {
    if (!dates) {
      return false;
    }
    const tooLate = dates[0] && current.diff(dates[0], "days") >= 7;
    const tooEarly = dates[1] && dates[1].diff(current, "days") >= 7;
    return !!tooEarly || !!tooLate;
  };

  return (
    <ConfigProvider prefixCls="x-coreui">
      <div className={classNames("picker-group-container", theme)}>
        {btnList && (
          <div className="date-btn-group">
            <ButtonRadio
              btnGroup={btnList}
              onChange={(data: any) => {
                setDateType(data.value);
                onChange(null, data.value);
              }}
            />
          </div>
        )}

        <div className="picker">
          <i className="picker-label">{pickerLabel[dateType]} </i>
          {dateType === DateType.DAY ? (
            maxSize == 1 ? (
              <DatePicker
                locale={locale}
                dropdownClassName="dashboard"
                defaultValue={defaultDate}
                onChange={(value: any) => {
                  onChange([value], dateType);
                }}
              />
            ) : (
              <RangePicker
                locale={locale}
                dropdownClassName="dashboard"
                disabledDate={disabledDate}
                defaultValue={[defaultDate, defaultDate]}
                value={dates}
                onCalendarChange={(val: any) => {
                  if (val) {
                    const diff = Math.ceil(val[0].diff(val[1], "days")) > 7;
                    if (diff) {
                      return;
                    }
                  }
                  setDates(val);
                }}
                onChange={(value: any) => {
                  onChange(value, dateType);
                }}
              />
            )
          ) : (
            <DatePickerCmp
              defaultDate={defaultDate}
              maxSize={maxSize || 7}
              maxTagCount={maxTagCount || 3}
              type={dateType}
              onChange={(values: any) => {
                onChange(values, dateType);
              }}
            />
          )}
        </div>
      </div>
    </ConfigProvider>
  );
};

export default DatepickerGroup;
