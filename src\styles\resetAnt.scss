// 背景
$bg: rgba(27, 32, 56, 1); // 表单,下拉框
$bgWeak: rgba(255, 255, 255, 0.12);
$bgLightGray: rgba(38, 43, 64, 1); // 日期
$bgShortGray: #d8d8d8;
$bgDark: #262b40; // Button
$bgDarkHover: rgba(0, 0, 0, 0.2);
$bgInput: rgba(10, 13, 26, 0.4); // 表单输入框背景
$bgPickerRangeHover: #1c1f37;
$bgTableHeader: #2e334d;
$bgTableEven: #202335;
$bgTableOdd: #292d45;
$bgTableHover: #3a3f57;
$bgDropDown: #3b3f54;
$scrollBar: #484C5F;
// 白色
$white: #ffffff;
// 灰色
$gray: #666;
// 警告
$waring: #ff574d;

// 业务主色
$themeNormal: #0d85ff;
$themeHover: #0869cc;
$themeDisabled: #054e99;

// 边框
$borderFocus: #0d85ff; // focus 边框
$borderLightGray: rgba(54, 57, 77, 1); // 表单输入框边框
$borderDarkGray: rgba(10, 13, 26, 1); // 日期组件框线
$borderDark: rgba(255, 255, 255, 0.16); // Button边框

//字体
$themeFontColor: rgba(255, 255, 255, 0.8);
$fontPlaceholder: rgba(255, 255, 255, 0.4);

// 字体
$sizeLargest: 36px;
$sizeLarger: 24px;
$sizeLarge: 20px;
$sizeMedium: 16px;
$sizeNormal: 14px;
$sizeSmall: 12px;

// 高度
$height32: 32px;