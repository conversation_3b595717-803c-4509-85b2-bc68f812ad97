import React, { useEffect, useState, useRef } from "react";
import {
  DatePicker,
  Select,
  Form,
  Cascader,
  Input,
  Radio,
  Checkbox,
  Switch,
  Row,
  Col,
  Button,
  InputNumber,
  TimePicker,
  ConfigProvider,
} from "antd";
import locale from "antd/es/date-picker/locale/zh_CN";
import type { FormProps, FieldItem, FormConfig } from "./formConfig";
import "./index.scss";
import { addGlobalEventListener, removeGlobalEventListener } from "@utils/emit";
import FileUpload from "../FileUpload";
import { filterOption } from "@utils/index";
import { cloneDeep } from "lodash";
import classNames from "classnames";
import { SearchButtons } from "./SearchButtons";

const { RangePicker } = DatePicker;

const CommonForm = (props: FormProps) => {
  const {
    name,
    theme = "light",
    formConfig,
    layout = "horizontal",
    formType = "edit",
    labelAlign,
    className,
    colon,
    defaultValue = {},
    onValueChange,
    getFormInstance,
    searchBtnClstag,
    resetBtnClstag,
    onResetClick,
    onSearchClick,
    onFieldFocus,
    initLink = false,
  } = props;
  const [formData, setFormData] = useState<any>(null);
  const formDataRef = useRef<any>(null);
  const [commonFormRef] = Form.useForm();
  const [selectList, setSelectList] = useState<any>({});
  const [uploading, setUploading] = useState(false);
  const [, forceUpload] = useState<number>(0);
  const showCheckedStrategyMap = new Map([
    ["SHOW_PARENT", Cascader.SHOW_PARENT],
    ["SHOW_CHILD", Cascader.SHOW_CHILD],
  ]);
  useEffect(() => {
    const deepCloneVal = cloneDeep(formConfig);
    setFormData(deepCloneVal);
    formDataRef.current = deepCloneVal;
    const cb = (data: { name: string; config: FormConfig }) => {
      if (data.name === props.name) {
        setFormData({ ...data.config });
        formDataRef.current = { ...data.config };
        setSelectList({});
      }
    };
    const updateOneConfig = (data: {
      name: string;
      fieldName: string;
      config: any;
    }) => {
      if (data.name === props.name) {
        formDataRef.current = {
          ...formDataRef.current,
          fields: formDataRef.current.fields?.map((v: any) => {
            if (v.fieldName === data.fieldName) {
              return data.config;
            } else {
              return v;
            }
          }),
        };
        setFormData({ ...formDataRef.current });
      }
    };
    addGlobalEventListener("FORCE_UPDATE_CONFIG", cb);
    addGlobalEventListener("FORCE_UPDATE_ONE_CONFIG", updateOneConfig);
    return () => {
      removeGlobalEventListener("FORCE_UPDATE_CONFIG", cb);
      removeGlobalEventListener("FORCE_UPDATE_ONE_CONFIG", updateOneConfig);
    };
  }, [JSON.stringify(formConfig)]);

  useEffect(() => {
    commonFormRef.setFieldsValue(defaultValue);
    defaultValue &&
      Object.keys(defaultValue).forEach((v) => {
        onFieldsChange(v, defaultValue[v], initLink ? "operate" : "init");
      });
    forceUpload(Date.now());
  }, [JSON.stringify(defaultValue)]);

  useEffect(() => {
    getFormInstance && getFormInstance(commonFormRef);
  }, []);

  const customReport = (clstag: any) => {
    try {
      (window as any).__qd__ &&
        (window as any).__qd__.click({
          cls: clstag,
        });
    } catch (e) {
      console.log(e);
    }
  };

  const renderFieldItem = (dataItem: FieldItem) => {
    const {
      fieldName,
      type,
      options,
      placeholder,
      multiple,
      showSearch,
      maxLength,
      allowClear,
      disabled,
      showSelectAll,
      autoSize,
      showCount,
      maxTagCount,
      defaultChecked,
      mapRelation,
      renderFunc,
      changeOnSelect,
      showCheckedStrategy,
      labelInValue = true,
      showNow = false,
      min = 0,
      max = 9999999,
      disabledDate,
      disabledTime,
      fileListType = "picture",
      uploadedFileList,
      accept,
      bucketName,
      LOPDN,
      getPreSignatureUrl,
      maxFileSize,
      unit = "MB",
      needConfirm = false,
      format,
      hideDisabledOptions = false,
      showTime = true,
      formatter,
      parser,
      precision,
      step,
      clstag,
      picker,
      onBlur,
    } = dataItem;
    switch (type) {
      case "input":
        return (
          <Input
            clstag={clstag}
            placeholder={placeholder ?? ""}
            allowClear={typeof allowClear === "undefined" ? true : allowClear}
            maxLength={maxLength}
            disabled={disabled}
            onBlur={(...args: any[]) => {
              onBlur && onBlur(args, commonFormRef.getFieldsValue() as any);
            }}
          />
        );
      case "inputNumber":
        return (
          <InputNumber
            clstag={clstag}
            placeholder={placeholder ?? ""}
            controls={false}
            formatter={formatter}
            parser={parser}
            precision={precision}
            min={min}
            max={max}
            step={step}
            maxLength={maxLength}
            disabled={disabled}
          />
        );
      case "radioGroup":
        return (
          <Radio.Group
            options={options}
            disabled={disabled}
            onChange={() => {
              customReport(clstag);
            }}
          />
        );
      case "textarea":
        return (
          <Input.TextArea
            clstag={clstag}
            placeholder={placeholder ?? ""}
            autoSize={autoSize === true ? { minRows: 2, maxRows: 6 } : autoSize}
            maxLength={maxLength}
            disabled={disabled}
            showCount={
              showCount && {
                formatter: ({ value, count, maxLength }) =>
                  `${count}/${maxLength}`,
              }
            }
          />
        );
      case "select":
        let _selectOptions: any[] = [];
        if (selectList[fieldName!]) {
          _selectOptions = selectList[fieldName!];
        } else if (options) {
          _selectOptions = options;
        }
        return (
          <Select
            fieldNames={mapRelation}
            placeholder={placeholder ?? ""}
            options={_selectOptions}
            showSearch={showSearch}
            labelInValue={labelInValue}
            disabled={disabled}
            mode={multiple ? "multiple" : undefined}
            allowClear={typeof allowClear === "undefined" ? true : allowClear}
            filterOption={showSearch && filterOption}
            onFocus={() => {
              onFieldFocus &&
                onFieldFocus(
                  dataItem.fieldName,
                  commonFormRef.getFieldsValue()
                );
              customReport(clstag);
            }}
          />
        );
      case "rangeTime":
        return (
          <RangePicker
            clstag={clstag}
            locale={locale}
            showTime={showTime}
            format={format ?? "YYYY-MM-DD HH:mm:ss"}
            disabled={disabled}
            showNow={showNow}
            needConfirm={needConfirm}
            picker={picker}
            disabledDate={disabledDate}
          />
        );
      case "timePicker":
        return (
          <TimePicker
            clstag={clstag}
            disabled={disabled}
            disabledDate={disabledDate}
            disabledTime={disabledTime}
            showNow={showNow}
            needConfirm={needConfirm}
            hideDisabledOptions={hideDisabledOptions}
          />
        );
      case "datePicker":
        return (
          <DatePicker
            clstag={clstag}
            allowClear={typeof allowClear === "undefined" ? true : allowClear}
            locale={locale}
            format={format ?? "YYYY-MM-DD"}
            disabled={disabled}
            disabledDate={disabledDate}
            disabledTime={disabledTime}
            needConfirm={needConfirm}
          />
        );
      case "dateTime":
        return (
          <DatePicker
            clstag={clstag}
            allowClear={typeof allowClear === "undefined" ? true : allowClear}
            locale={locale}
            showTime={showTime}
            format={format ?? "YYYY-MM-DD HH:mm:ss"}
            disabled={disabled}
            disabledDate={disabledDate}
            disabledTime={disabledTime}
            needConfirm={needConfirm}
          />
        );
      case "cascader":
        let _cascaderOptions: any[] = [];
        if (selectList[fieldName!]) {
          _cascaderOptions = selectList[fieldName!];
        } else if (options) {
          _cascaderOptions = options;
        }
        return (
          <Cascader
            fieldNames={mapRelation}
            options={_cascaderOptions}
            placeholder={placeholder ?? ""}
            maxTagCount={maxTagCount}
            showSearch={typeof showSearch === "undefined" ? true : showSearch}
            disabled={disabled}
            allowClear={typeof allowClear === "undefined" ? true : allowClear}
            multiple={multiple}
            showCheckedStrategy={
              typeof showCheckedStrategy == "undefined"
                ? Cascader.SHOW_CHILD
                : showCheckedStrategyMap.get(showCheckedStrategy)
            }
            changeOnSelect={
              typeof changeOnSelect === "undefined" ? true : changeOnSelect
            }
            onDropdownVisibleChange={(value) => {
              onFieldFocus &&
                onFieldFocus(
                  dataItem.fieldName,
                  commonFormRef.getFieldsValue()
                );
            }}
            onFocus={() => {
              customReport(clstag);
            }}
          />
        );
      case "switch":
        return <Switch defaultChecked={defaultChecked} />;
      case "checkboxGroup":
        return (
          <Checkbox.Group
            options={options}
            disabled={disabled}
            onChange={() => {
              customReport(clstag);
            }}
          />
        );
      case "ReactNode":
        const fieldValue = commonFormRef.getFieldValue(dataItem.fieldName);
        return <>{renderFunc && renderFunc(dataItem, fieldValue)}</>;
      case "upload":
        return (
          <FileUpload
            accept={accept!}
            bucketName={bucketName}
            fileListType={fileListType}
            LOPDN={LOPDN!}
            getPreSignatureUrl={getPreSignatureUrl!}
            uploadedFileList={uploadedFileList || []}
            uploadBtn={{
              type: fileListType === "picture" ? "icon" : "btn",
              btnText: fileListType === "picture" ? "上传图片" : "上传文件",
            }}
            maxCount={max}
            maxFileSize={maxFileSize}
            unit={unit}
            onStart={() => {
              setUploading(true);
              props.getUploadStatus &&
                props.getUploadStatus(true, "uploading", fieldName);
            }}
            onEnd={(fileKey: string) => {
              const filekeyList = commonFormRef.getFieldValue(fieldName) || [];
              const set = new Set(filekeyList);
              set.add(fileKey);
              commonFormRef.setFieldValue(fieldName, [...set]);
              setUploading(false);
              props.getUploadStatus &&
                props.getUploadStatus(false, "success", fieldName);
            }}
            onDelete={(fileKey: string) => {
              const filekeyList = commonFormRef.getFieldValue(fieldName) || [];
              const set = new Set(filekeyList);
              set.delete(fileKey);
              commonFormRef.setFieldValue(fieldName, [...set]);
              props.getUploadStatus &&
                props.getUploadStatus(false, "delete", fieldName);
            }}
            onError={() => {
              setUploading(false);
              props.getUploadStatus &&
                props.getUploadStatus(false, "error", fieldName);
            }}
          />
        );
      case "text":
        const value = commonFormRef.getFieldValue(dataItem.fieldName);
        return <span className="field-text">{value}</span>;
      default:
        return null;
    }
  };

  const onFieldsChange = (
    changedFieldName: any,
    val: any,
    type: "init" | "operate"
  ) => {
    if (!formDataRef.current?.linkRules) {
      return;
    }
    // 找到当前元素改变后需要的联动的表单项
    const hasLinkRule = Object.keys(formDataRef.current?.linkRules).includes(
      changedFieldName
    );
    if (!hasLinkRule) {
      return;
    }
    formDataRef.current?.linkRules[changedFieldName]?.forEach((item: any) => {
      const {
        linkFieldName,
        rule,
        dependenceData,
        disabledValue,
        fetchFunc,
        refreshFunc,
      } = item;
      let needChangeField: FieldItem = formDataRef.current.fields?.find(
        (f: FieldItem) => f.fieldName === linkFieldName
      );
      switch (rule) {
        case "visible":
          needChangeField.hidden = !dependenceData.includes(val?.value ?? val);
          break;
        case "refresh":
          if (refreshFunc) {
            const data = refreshFunc(val, needChangeField);
            needChangeField.options = data;
          }
          commonFormRef.setFieldsValue({
            [linkFieldName]: defaultValue[linkFieldName],
          });
          break;
        case "clear":
          if (type === "operate") {
            commonFormRef.setFieldsValue({
              [linkFieldName]: needChangeField?.multiple ? undefined : null,
            });
          }
          break;
        case "fetchData":
          fetchFunc(val, commonFormRef).then((res: any[]) => {
            setSelectList((selectList: any) => ({
              ...selectList,
              [linkFieldName]: res,
            }));
          });
          break;
        case "valueDisable":
          if (needChangeField.options) {
            needChangeField.options = needChangeField.options.map((v: any) => {
              if (
                disabledValue.includes(v.value) &&
                dependenceData.includes(val)
              ) {
                return {
                  ...v,
                  disabled: true,
                };
              } else {
                return { ...v, disabled: false };
              }
            });
          }
          break;
        case "fieldItemDisable":
          needChangeField.disabled = dependenceData.includes(val);
          break;
      }
    });
    // setTimeout(() => {
    setFormData({ ...formDataRef.current });
    // }, 100);
  };

  const makeLayout = () => {
    let titleMaxLength = 4;
    formData?.fields.forEach((item: any) => {
      if (item.label?.length && item.label?.length > titleMaxLength) {
        titleMaxLength = item.label.length || item.label.length;
      }
    });
    titleMaxLength = titleMaxLength >= 9 ? 9 : titleMaxLength;

    return {
      labelCol: { span: titleMaxLength + 2 },
      wrapperCol:
        layout === "inline"
          ? { span: 24 - (titleMaxLength + 2) }
          : { span: 23 - (titleMaxLength + 1) },
    };
  };

  useEffect(() => {
    if (theme === "dark") {
      document.body.classList.add("x-coreui-dark-theme");
    }
  }, [theme]);
  return (
    <ConfigProvider prefixCls="x-coreui">
      <div
        className={classNames({
          "dark-theme": theme == "dark",
          "x-coreui-searchform-container": formType == "search",
          "x-coreui-common-form-container": formType != "search",
        })}
      >
        <Form
          {...makeLayout()}
          initialValues={defaultValue}
          name={name || "complex-form"}
          labelAlign={labelAlign ?? "right"}
          layout={layout}
          colon={colon ?? true}
          form={commonFormRef}
          autoComplete="off"
          className={className || ""}
          style={{
            alignItems: "center",
          }}
          onValuesChange={(changedValues, allValues) => {
            const changedFieldName = Object.keys(changedValues)[0];
            const changedVal = changedValues[changedFieldName];
            onValueChange &&
              onValueChange(commonFormRef.getFieldsValue(), changedFieldName);
            changedFieldName &&
              onFieldsChange(changedFieldName, changedVal, "operate");
          }}
        >
          <Row gutter={24} align="middle" style={{ width: "100%" }}>
            {formData?.fields?.map((item: FieldItem, index: number) => {
              if (item.childrenList) {
                const cWidth = Math.floor(100 / item.childrenList.length);
                return (
                  !item.hidden && (
                    <Col
                      xxl={layout === "inline" ? (item.xxl ? item.xxl : 4) : 23}
                      xl={layout === "inline" ? (item.xl ? item.xl : 6) : 23}
                      lg={layout === "inline" ? (item.lg ? item.lg : 8) : 23}
                      md={item.md ? item.md : 23}
                      key={index}
                    >
                      <Form.Item
                        label={item.label}
                        style={{ marginBottom: 0 }}
                        labelCol={item.labelCol && item.labelCol}
                        wrapperCol={item.wrapperCol && item.wrapperCol}
                        tooltip={item.tooltip && item.tooltip}
                        required={
                          typeof item.required === "undefined"
                            ? false
                            : item.required
                        }
                      >
                        {item.childrenList.map(
                          (name: string, index: number) => {
                            const _cField = formData?.fields.find(
                              (v: FieldItem) => v.fieldName === name
                            );
                            const _marginLeft =
                              _cField?.marginLeft || _cField?.marginLeft === 0
                                ? _cField.marginLeft
                                : 8;
                            const _marginRight =
                              _cField?.marginRight || _cField?.marginRight === 0
                                ? _cField.marginRight
                                : 8;
                            return (
                              !_cField.hidden && (
                                <Form.Item
                                  labelCol={
                                    _cField?.labelCol && _cField.labelCol
                                  }
                                  wrapperCol={
                                    _cField?.wrapperCol && _cField.wrapperCol
                                  }
                                  name={_cField?.fieldName}
                                  label={_cField?.label}
                                  help={_cField?.help}
                                  extra={item.extra}
                                  rules={_cField?.validatorRules ?? []}
                                  className={
                                    "field_" + _cField?.fieldName?.toString()
                                  }
                                  style={{
                                    display: "inline-block",
                                    width:
                                      _cField?.width ??
                                      `calc(${cWidth}% - ${_marginLeft}px - ${_marginRight}px)`,
                                    marginLeft: `${_marginLeft}px`,
                                    marginRight: `${_marginRight}px`,
                                  }}
                                >
                                  {renderFieldItem(_cField)}
                                </Form.Item>
                              )
                            );
                          }
                        )}
                      </Form.Item>
                    </Col>
                  )
                );
              } else if (!item.isChild) {
                return (
                  !item.hidden && (
                    <Col
                      xxl={layout === "inline" ? (item.xxl ? item.xxl : 4) : 23}
                      xl={layout === "inline" ? (item.xl ? item.xl : 6) : 23}
                      lg={layout === "inline" ? (item.lg ? item.lg : 8) : 23}
                      md={item.md ? item.md : 23}
                      key={item.fieldName?.toString()}
                    >
                      <Form.Item
                        labelCol={item.labelCol && item.labelCol}
                        wrapperCol={item.wrapperCol && item.wrapperCol}
                        name={item.fieldName}
                        label={item.label}
                        help={item.help}
                        extra={item.extra}
                        rules={item.validatorRules ?? []}
                        className={"field_" + item.fieldName?.toString()}
                        tooltip={item.tooltip && item.tooltip}
                      >
                        {renderFieldItem(item)}
                      </Form.Item>
                    </Col>
                  )
                );
              }
            })}
          </Row>
        </Form>
        {formType === "search" && (
          <SearchButtons
            onReset={() => {
              commonFormRef.setFieldsValue(defaultValue);
              Object.keys(defaultValue).forEach((v) => {
                onFieldsChange(v, defaultValue[v], "operate");
              });
              onResetClick?.();
            }}
            onSearch={() => onSearchClick?.(commonFormRef.getFieldsValue())}
            resetBtnClstag={resetBtnClstag}
            searchBtnClstag={searchBtnClstag}
          />
        )}
      </div>
    </ConfigProvider>
  );
};

export type { FormConfig, FieldItem, FormProps };
export default React.memo(CommonForm);
