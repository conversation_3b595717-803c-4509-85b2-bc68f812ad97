import { ReactNode } from "react";
import { AnyFunc } from "../../global";
import { Dayjs } from "dayjs";
type DisabledTime = (now: Dayjs) => {
  disabledHours?: () => number[];
  disabledMinutes?: (selectedHour: number) => number[];
  disabledSeconds?: (selectedHour: number, selectedMinute: number) => number[];
  disabledMilliseconds?: (
    selectedHour: number,
    selectedMinute: number,
    selectedSecond: number
  ) => number[];
};
export interface FieldItem {
  // 通用属性
  type?:
    | "input"
    | "select"
    | "rangeTime"
    | "cascader"
    | "textarea"
    | "radioGroup"
    | "checkboxGroup"
    | "datePicker"
    | "timePicker"
    | "customize"
    | "dateTime"
    | "switch"
    | "upload"
    | "inputNumber"
    | "ReactNode"
    | "text";
  renderFunc?: (data: any, value?: any) => ReactNode;
  //upload
  fileListType?: "picture" | "file";
  accept?: string;
  bucketName?: string;
  LOPDN?: string;
  getPreSignatureUrl?: string;
  maxFileSize?: number;
  unit?: "MB" | "GB" | "KB";
  maxFileCount?: number;
  uploadedFileList?: any[];
  fieldName?: string;
  label?: ReactNode;
  placeholder?: string;
  hidden?: boolean; // 是否隐藏字段（依然会收集和校验字段）
  required?: boolean; //必填样式,用于父项（有childrenList属性的表单项）
  validatorRules?:
    | {
        // 校验规则
        required: boolean;
        message: string;
        validator?: (rule: any, value: any) => Promise<any>;
      }[]
    | any[]
    | null;
  help?: ReactNode;
  labelCol?: { span: number };
  wrapperCol?: { span: number };
  childrenList?: string[]; // 多个表单项合并成一个
  width?: string;
  marginLeft?: number;
  marginRight?: number;
  disabled?: boolean;
  isChild?: boolean;
  xxl?: number;
  xl?: number;
  lg?: number;
  md?: number;
  departmentParams?: Object;
  tooltip?: string | ReactNode;
  clstag?: string; // 埋点坑位id

  // switch
  defaultChecked?: boolean;

  // 输入框
  maxLength?: number; // 输入框长度限制
  max?: number; // 数字输入框最大值
  min?: number; // 数字输入框最小值
  autoSize?: boolean | object; // textArea自适应内容高度
  showCount?: boolean;
  formatter?: (value: any) => string;
  parser?: any;
  precision?: number;
  step?: number;

  // 选择: 单多选、级联、单复选
  options?: any[];
  showSearch?: boolean; // 是否支持搜索
  allowClear?: boolean;
  maxTagCount?: number;
  multiple?: boolean;
  showSelectAll?: boolean;
  mapRelation?: object;
  changeOnSelect?: boolean;
  labelInValue?: boolean;
  showCheckedStrategy?: "SHOW_PARENT" | "SHOW_CHILD";
  checkable?: boolean;

  // 时间选择框
  disabledTime?: DisabledTime;
  disabledDate?: AnyFunc;
  showNow?: boolean;
  needConfirm?: boolean;
  format?: string;
  hideDisabledOptions?: boolean;
  showTime?: Object | boolean;
}

export interface FormProConfig {
  fields: FieldItem[];
  linkRules?: {
    // 变化的表单项
    [fieldName: string]: {
      // 表单项变化后导致哪些表单项联动变化
      targetFields: string[];
      // 当前元素要发生什么变化 fetchData->获取数据(比如请求下拉框内容) clear->清空值 refresh->更新为初始值 visible->表单项是否展示 valueDisable->数据不可用 fieldItemDisable->表单项不可用
      rule:
        | "fetchData"
        | "clear"
        | "refresh"
        | "visible"
        | "visibility"
        | "hidden"
        | "valueDisable"
        | "fieldItemDisable";
      dependenceData?: any[]; // 依赖元素的值变为哪个时当前元素发生变化
      disabledValue?: any[]; // rule为valueDisable时，哪个值不可用
      fetchFunc?: Function; // rule为fetchData时，请求数据的函数
      refreshFunc?: Function;
    }[];
  };
}
