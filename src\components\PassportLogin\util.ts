export const getCookie = (name: string) => {
  if (!name) {
    return ''
  }
  const strCookie = document.cookie
  const arrCookie = strCookie.split('; ')
  for (let i = 0; i < arrCookie.length; i++) {
    const arr = arrCookie[i].split('=')
    if (name == arr[0]) {
      return arr[1]
    }
  }
  return ''
}
export const clearCookie = (name: string) => {
  const d = new Date();
  d.setTime(d.getTime() + (-1 * 24 * 60 * 60 * 1000));
  const expires = "expires=" + d.toUTCString();
  document.cookie = name + "=" + '' + "; " + expires;
}