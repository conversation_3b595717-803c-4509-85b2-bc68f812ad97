import React from "react";
import PropTypes from "prop-types";
import CommonForm from "@components/CommonForm";

export const Form: React.FC<any> = (props) => {
  return (
    <div className={`form-container`}>
      <CommonForm {...props} />
    </div>
  );
};

const _FormConfig = {
  fields: [
    {
      fieldName: 'productKey',
      label: '请选择产品型号',
      placeholder: '请选择产品',
      type: 'select',
      labelInValue: false,
      allowClear: false,
      labelCol: { span: 12 },
      wrapperCol: { span: 12 },
      xl: 12,
      xxl: 12,
      validatorRules: [
        {
          required: true,
          message: '请选择产品',
        },
      ],
    },
    {
      fieldName: 'productModelNoList',
      label: '',
      type: 'select',
      placeholder: '请选择型号',
      multiple: true,
      labelInValue: false,
      wrapperCol: { span: 12 },
      xl: 12,
      xxl: 12,
      validatorRules: [
        {
          required: true,
          message: '请选择型号',
        },
      ],
    },
    {
      fieldName: 'appType',
      label: '选择发布内容',
      type: 'select',
      placeholder: '请选择升级包类型',
      labelInValue: false,
      labelCol: { span: 12 },
      wrapperCol: { span: 12 },
      xl: 12,
      xxl: 12,
      validatorRules: [
        {
          required: true,
          message: '请选择升级包类型',
        },
      ],
    },
    {
      fieldName: 'appName',
      label: '',
      type: 'select',
      labelInValue: false,
      wrapperCol: { span: 24 },
      xl: 6,
      xxl: 6,
      placeholder: '请选择固件/应用',
      validatorRules: [
        {
          required: true,
          message: '请选择固件/应用',
        },
      ],
    },
    {
      fieldName: 'appVersionNumber',
      label: '',
      type: 'select',
      labelInValue: false,
      wrapperCol: { span: 24 },
      xl: 6,
      xxl: 6,
      placeholder: '请选择版本号',
      validatorRules: [
        {
          required: true,
          message: '请选择版本号',
        },
      ],
    },
    {
      fieldName: 'description',
      label: '备注',
      type: 'textarea',
      placeholder: '请输入',
      autoSize: { minRows: 2, maxRows: 6 },
      maxLength: 150,
      showCount: true,
      labelCol: { span: 6 },
      wrapperCol: { span: 17 },
      xl: 23,
      xxl: 23,
    },
  ],
  linkRules: {
    productKey: [
      {
        linkFieldName: 'productModelNoList',
        rule: 'fetchData',
      },
      {
        linkFieldName: 'appType',
        rule: 'clear',
      },
    
      {
        linkFieldName: 'appName',
        rule: 'clear',
      },
      {
        linkFieldName: 'appVersionNumber',
        rule: 'clear',
      },
    ],
    appType: [
      {
        linkFieldName: 'appName',
        rule: 'fetchData',
      },
      {
        linkFieldName: 'appName',
        rule: 'clear',
      },
      {
        linkFieldName: 'appVersionNumber',
        rule: 'clear',
      },
    ],
    appName: [
      {
        linkFieldName: 'appVersionNumber',
        rule: 'clear',
      },
    ],
  },
};

Form.propTypes = {
  formConfig: PropTypes.shape({
    fields: PropTypes.arrayOf(
      PropTypes.shape({
        type: PropTypes.oneOf([
          "input",
          "select",
          "rangeTime",
          "cascader",
          "textarea",
          "radioGroup",
          "checkboxGroup",
          "datePicker",
          "customize",
          "dateTime",
          "switch",
          "inputNumber",
          "ReactNode",
        ]).isRequired,
        renderFunc: PropTypes.func,
        fieldName: PropTypes.string,
        label: PropTypes.node,
        placeholder: PropTypes.string,
        hidden: PropTypes.bool,
        validatorRules: PropTypes.arrayOf(
          PropTypes.shape({
            required: PropTypes.bool,
            message: PropTypes.string,
            validator: PropTypes.func,
          })
        ),
        help: PropTypes.node,
        labelCol: PropTypes.shape({
          span: PropTypes.number.isRequired,
        }),
        wrapperCol: PropTypes.shape({
          span: PropTypes.number.isRequired,
        }),
        childrenList: PropTypes.arrayOf(PropTypes.string),
        width: PropTypes.string,
        marginLeft: PropTypes.number,
        marginRight: PropTypes.number,
        disabled: PropTypes.bool,
        isChild: PropTypes.bool,
        xxl: PropTypes.number,
        xl: PropTypes.number,
        lg: PropTypes.number,
        md: PropTypes.number,
        specialFetch: PropTypes.oneOf(["commonDown", "station", "city"]),
        departmentParams: PropTypes.object,
        dropDownKey: PropTypes.string,
        dropDownListKey: PropTypes.string,
        tooltip: PropTypes.oneOf([PropTypes.string, PropTypes.node]),
        defaultChecked: PropTypes.bool,
        maxLength: PropTypes.number,
        autoSize: PropTypes.bool,
        showCount: PropTypes.number,
        options: PropTypes.array,
        showSearch: PropTypes.bool,
        allowClear: PropTypes.bool,
        maxTagCount: PropTypes.number,
        multiple: PropTypes.bool,
        showSelectAll: PropTypes.bool,
        mapRelation: PropTypes.object,
        changeOnSelect: PropTypes.bool,
        labelInValue: PropTypes.bool,
        showCheckedStrategy: PropTypes.oneOf(["SHOW_PARENT", "SHOW_CHILD"]),
        checkable: PropTypes.bool,
        disabledTime: PropTypes.func,
        disabledDate: PropTypes.func,
        showNow: PropTypes.bool,
      })
    ),
    linkRules: PropTypes.any,
  }),
  name: PropTypes.string,
  layout: PropTypes.oneOf(["horizontal", "vertical", "inline"]),
  defaultValue: PropTypes.any,
  formType: PropTypes.oneOf(["search", "edit"]),
  colon: PropTypes.bool,
  className: PropTypes.string,
  labelAlign: PropTypes.oneOf(["left", "right"]),
  onValueChange: PropTypes.func,
  getFormInstance: PropTypes.func,
  onResetClick: PropTypes.func,
  onSearchClick: PropTypes.func,
  onFieldFocus: PropTypes.func,
};

Form.defaultProps = {
  formConfig: _FormConfig,
  name: "search",
  layout: "horizontal",
  defaultValue: {
    technicalSupportDescription: "测试文字",
  },
};
