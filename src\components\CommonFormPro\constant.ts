export enum DropDownType {
  INPUT = 'INPUT',
  SELECT = 'SELECT',
  DATEPICKER = 'DATEPICKER',
  MULTIPLESELECT = 'MULTIPLESELECT',
  CASCADER = 'CASCADER',
}

export enum dropDownKey {
  // 配置模板管理
  positionList = 'positionList', // 所在位置
  enableEnumList = 'enableEnumList', // 模板状态
  productTypeList = 'productTypeList', // 所属产品
  // 车型配置管理
  vehicleTypeConfInfoStateList = 'vehicleTypeConfInfoStateList', // 车型配置状态

  // 车辆配置与发布
  vehicleOwnerUseCaseList = 'vehicleOwnerUseCaseList', // 车辆归属方
  confInfoStatusList = 'confInfoStatusList', // 配置发布状态
  issueTaskVehicleStatusList = 'issueTaskVehicleStatusList', // 最近一次升级结果
  pushStreamDeviceList = 'pushStreamDeviceList', // 推流设备
  videoCameraList = 'videoCameraList', // 推流相机
  vehicleBusinessTypeList = 'vehicleBusinessTypeList', // 车辆类型

  // 发布计划管理
  appNameList = 'appNameList',
  issueTaskStatusList = 'issueTaskStatusList', // 发布计划状态
}

export enum dropDownListKey {
  // 配置模板管理
  positionList = 'positionList', // 所在位置
  enableEnumList = 'enableEnumList', // 模板状态
  productTypeList = 'productTypeList', // 所属产品
  // 车型配置管理
  vehicleTypeConfInfoStateList = 'vehicleTypeConfInfoStateList', // 车型配置状态

  // 车辆配置与发布
  vehicleConfTemplateList = 'vehicleConfTemplateList',
  vehicleOwnerUseCaseList = 'vehicleOwnerUseCaseList', // 车辆归属方
  confInfoStatusList = 'confInfoStatusList', // 配置发布状态
  issueTaskVehicleStatusList = 'issueTaskVehicleStatusList', // 最近一次升级结果
  pushStreamDeviceList = 'pushStreamDeviceList', // 推流设备
  videoCameraList = 'videoCameraList', // 推流相机
  vehicleBusinessTypeList = 'vehicleBusinessTypeList', //  车辆类型

  // 发布计划管理
  appNameList = 'appNameList',
  issueTaskStatusList = 'issueTaskStatusList', // 发布计划状态
}
