import { StorybookConfig } from "@storybook/react-webpack5";
const path = require("path");
const config: StorybookConfig = {
  stories: [
    "../stories/**/*.mdx",
    "../stories/**/*.stories.@(js|jsx|mjs|ts|tsx)",
  ],
  addons: [
    "@storybook/addon-links",
    "@storybook/addon-essentials",
    "@storybook/addon-onboarding",
    "@storybook/addon-interactions",
  ],
  framework: {
    name: "@storybook/react-webpack5",
    options: {},
  },
  webpack: (config, options) => {
    options.cache!.set = () => Promise.resolve();
    return config;
  },
  webpackFinal: async (config: any) => {
    // 添加以下代码
    // 加载.scss文件
    config.module.rules.push({
      test: /\.s[ac]ss$/i,
      use: [
        "style-loader",
        "css-loader",
        {
          loader: "sass-loader",
          options: {
            // 启用Sass的import功能
            sassOptions: {
              importResolve: {
                extensions: [".scss", ".sass"],
              },
            },
            additionalData: `@import "${path.resolve(__dirname, "../src/styles/global-variables.scss").replace(/\\/g, "/")}";`,
          },
        },
      ],
      include: path.resolve(__dirname, "../"),
    });

    // 加载React组件
    config.resolve.alias = {
      ...config.resolve.alias,
      "@components": path.resolve(__dirname, "../src/components"),
      "@utils": path.resolve(__dirname, "../src/utils"),
    };
    return config;
  },
  docs: {
    autodocs: "tag",
  },
};
export default config;
