const fs = require("fs");
const path = require("path");
const webpack = require("webpack");
const StaticConfig = require("./static");
const externals = require("./externals");
// 使用 src/index.ts 作为主入口
const entry = {
  index: path.resolve(__dirname, "../src/index.ts"),
};

// 同时扫描 components 目录下的其他文件作为额外入口
const publicPath = "src";
const cmpDir = path.resolve(__dirname, "../", publicPath, "components");

function formatEntries(filePath, dirent) {
  const reg = /\.(js|jsx|ts|tsx)$/;
  const relativePath = path.relative(cmpDir, filePath);

  if (dirent.name.match(reg) && dirent.name !== "index.ts") {
    // 作为入口文件，但排除 index.ts（因为已经在主入口中）
    const entryKey = "components/" + relativePath.replace(reg, "");
    entry[entryKey] = path.join(cmpDir, relativePath);
  }
}

function readFiles(currentDirPath) {
  fs.readdirSync(currentDirPath, { withFileTypes: true })?.forEach(function (
    dirent
  ) {
    const filePath = path.join(currentDirPath, dirent.name);
    if (dirent.isFile()) {
      formatEntries(filePath, dirent);
    } else if (dirent.isDirectory()) {
      readFiles(filePath, dirent);
    }
  });
}

readFiles(cmpDir);
const config = {
  mode: "production",
  entry: entry,
  output: {
    filename: "[name].js",
    path: path.resolve(__dirname, "../lib"),
    library: "coreUI",
    libraryTarget: "umd",
  },
  devtool: "source-map",
  resolve: {
    extensions: [
      ".tsx",
      ".ts",
      ".jsx",
      ".js",
      ".json",
      ".png",
      ".css",
      ".less",
    ],
    alias: {
      "@components": path.resolve(__dirname, "../src/components"),
      "@utils": path.resolve(__dirname, "../src/utils"),
    },
  },
  module: {
    rules: [
      {
        test: /\.(js|jsx|ts|tsx)$/,
        exclude: /(node_modules|bower_components)/,
        use: [
          {
            loader: require.resolve("babel-loader"),
            options: {
              babelrc: false,
              presets: [
                require.resolve("@babel/preset-react"),
                [
                  require.resolve("@babel/preset-env"),
                  {
                    corejs: 2,
                    useBuiltIns: "usage",
                  },
                ].filter(Boolean),
                require.resolve("@babel/preset-typescript"),
              ],
              plugins: [
                [
                  "@babel/plugin-proposal-class-properties",
                  {
                    loose: true,
                  },
                ],
                ["@babel/plugin-proposal-decorators", { legacy: true }],
                [
                  "@babel/plugin-proposal-private-property-in-object",
                  { loose: true },
                ],
                ["@babel/plugin-proposal-private-methods", { loose: true }],
                "@babel/plugin-transform-arrow-functions",
              ],
            },
          },
        ],
      },
      ...StaticConfig.rules(),
    ],
  },
  externals: externals,
  plugins: [
    ...StaticConfig.plugins(),
    new webpack.LoaderOptionsPlugin({
      debug: true,
    }),
  ],
};

module.exports = config;
