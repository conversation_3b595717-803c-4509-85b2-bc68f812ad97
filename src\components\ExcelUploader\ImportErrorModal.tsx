import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Col } from 'antd';
import { WarningOutlined, CheckCircleOutlined, CloseCircleOutlined, DownloadOutlined } from '@ant-design/icons';
import { ImportErrorModalProps } from './types';
import './ExcelUploader.scss';

const ImportErrorModal: React.FC<ImportErrorModalProps> = ({
  visible,
  onClose,
  importResult,
  onDownload
}) => {
  const { totalCount, successCount, errorCount } = importResult;
  
  return (
    <Modal
      title={
        <div className="error-modal-title">
          <WarningOutlined style={{ color: '#faad14', marginRight: 8 }} />
          导入结果
        </div>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={520}
      maskClosable={false}
      className="import-error-modal"
    >
      <div className="error-modal-content">
        <div className="import-summary">
          <Row gutter={24}>
            <Col span={8}>
              <div className="summary-item">
                <div className="summary-label">总数据</div>
                <div className="summary-value total">{totalCount}</div>
              </div>
            </Col>
            <Col span={8}>
              <div className="summary-item">
                <div className="summary-label">
                  <CheckCircleOutlined style={{ color: '#52c41a' }} /> 成功
                </div>
                <div className="summary-value success">{successCount}</div>
              </div>
            </Col>
            <Col span={8}>
              <div className="summary-item">
                <div className="summary-label">
                  <CloseCircleOutlined style={{ color: '#ff4d4f' }} /> 失败
                </div>
                <div className="summary-value error">{errorCount}</div>
              </div>
            </Col>
          </Row>
        </div>

        {errorCount > 0 && (
          <div className="error-tips">
            <p>失败原因可能包括：</p>
            <ul>
              <li>必填字段为空</li>
              <li>数据格式不正确</li>
              <li>数据不在允许范围内</li>
            </ul>
            <p className="download-tip">您可以下载失败数据，查看具体原因后修改重新导入</p>
          </div>
        )}

        <div className="error-modal-footer">
          <Button onClick={onClose}>关闭</Button>
          {errorCount > 0 && (
            <Button type="primary" onClick={onDownload} icon={<DownloadOutlined />}>
              下载失败数据
            </Button>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default ImportErrorModal;
