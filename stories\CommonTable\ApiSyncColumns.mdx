import { Meta, Story, Canvas } from '@storybook/blocks';
import * as CommonTableStories from './CommonTable.stories';

<Meta title="CommonTable/ApiSyncColumns" />

# 列配置 API 同步功能

CommonTable 组件现在支持通过 API 获取和存储列配置信息，使用户可以在多个设备之间同步他们的列配置。

## 基本用法

列配置 API 同步功能允许用户通过 API 获取和存储列配置信息，使用户可以在多个设备之间同步他们的列配置。这对于需要在多个设备上保持一致的表格视图特别有用。

<Canvas of={CommonTableStories.ConfigurableColumnsLight} />

## 如何使用

要在你的 CommonTable 中启用列配置 API 同步功能，需要添加以下属性：

```tsx
import React from 'react';
import { CommonTable } from '@/components';

const MyTableWithApiSync = () => {
  const tableKey = 'my-unique-table-key';
  
  return (
    <CommonTable
      tableListData={{ list: dataSource, totalNumber: dataSource.length }}
      columns={columns}
      rowKey="id"
      tableKey={tableKey}
      showColumnSetting={true}
      columnsState={{
        persistenceType: 'api',
        api: {
          // API 路径
          fetchPath: '/api/column-settings/query',
          savePath: '/api/column-settings/save',
          // 是否使用绝对路径
          useAbsoluteURL: false,
          // LOPDN 参数
          LOPDN: 'your-lopdn-value',
          // 超时时间
          timeout: 30000
        }
      }}
    />
  );
};
```

## 属性说明

### columnsState.api 属性

| 属性 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| fetchPath | 获取列配置的 API 路径 | string | - |
| savePath | 保存列配置的 API 路径 | string | - |
| useAbsoluteURL | 是否使用绝对路径 | boolean | false |
| LOPDN | LOPDN 参数，用于请求头 | string | - |
| timeout | 请求超时时间 | number | 15000 |

## 数据结构

### 请求数据结构

获取列配置的请求：

```json
// POST /api/column-settings/query
{
  "configTypeList": ["my-unique-table-key"]
}
```

保存列配置的请求：

```json
// POST /api/column-settings/save
{
  "configType": "my-unique-table-key",
  "configParams": {
    "column1": { "show": true, "fixed": "left", "order": 0 },
    "column2": { "show": true, "fixed": undefined, "order": 1 },
    "column3": { "show": false, "fixed": "right", "order": 2 }
  }
}
```

### 响应数据结构

获取列配置的响应：

```json
{
  "data": [
    {
      "configType": "my-unique-table-key",
      "configParams": {
        "column1": { "show": true, "fixed": "left", "order": 0 },
        "column2": { "show": true, "fixed": undefined, "order": 1 },
        "column3": { "show": false, "fixed": "right", "order": 2 }
      }
    }
  ]
}
```

## 实现细节

1. **请求头**：API 请求会在请求头中添加 `LOP-DN` 字段，值为传入的 `LOPDN` 参数。

2. **错误处理**：如果 API 请求失败，组件会回退到默认列配置，并在控制台输出错误信息。

3. **加载状态**：在从 API 获取列配置时，组件会显示加载指示器。

4. **持久化类型**：可以通过 `persistenceType` 属性切换不同的持久化方式：
   - `'localStorage'`：使用浏览器的 localStorage 存储列配置
   - `'sessionStorage'`：使用浏览器的 sessionStorage 存储列配置
   - `'api'`：使用 API 存储列配置

5. **重置功能**：点击重置按钮时，会将列配置重置为默认值，并通过 API 保存这些默认值。

## 后端 API 实现建议

为了支持这个功能，后端需要提供以下 API 端点：

1. **POST /api/column-settings/query**：获取列配置列表
   - 请求体：`{ configTypeList: string[] }`
   - 返回：`{ data: { configType: string, configParams: Record<string, ColumnState> }[] }`

2. **POST /api/column-settings/save**：保存列配置
   - 请求体：`{ configType: string, configParams: Record<string, ColumnState> }`
   - 返回：保存结果

这些 API 端点可以根据您的具体需求进行调整，例如添加认证、权限控制等。
