import React from 'react';
import { AnyFunc } from '../../../global';

export interface MonthHeaderProps {
  currentDate: any;
  changeDate: AnyFunc;
}

const MonthHeader: React.FC<MonthHeaderProps> = (props) => {
  const { currentDate, changeDate } = props;
  return (
    <div className="month-header">
      <span
        className="pre-btn"
        onClick={() => {
          const newYear = currentDate.year() - 1;
          changeDate(currentDate.year(newYear));
        }}
      >
        {'<<'}
      </span>
      <span className="date-regin">{currentDate.year()}年</span>
      <span
        className="next-btn"
        onClick={() => {
          const newYear = currentDate.year() + 1;
          changeDate(currentDate.year(newYear));
        }}
      >
        {'>>'}
      </span>
    </div>
  );
};

export default MonthHeader;
