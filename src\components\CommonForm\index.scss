@import '../../styles/resetAnt';

.x-coreui-searchform-container,
.x-coreui-common-form-container {
  background-color: white;
  padding-top: 24px;
  padding-bottom: 12px;
  padding-left: 12px;

  .#{$ant-prefix}-form {
    .#{$ant-prefix}-form-item {
      margin-bottom: 16px;
    }
  }

  .searchbtns {
    margin-right: 15px;
  }

  .#{$ant-prefix}-picker {
    width: 100%;
  }

  .#{$ant-prefix}-input-number {
    width: 100%;
  }
}

.dark-theme.x-coreui-common-form-container {
  background-color: #0a0d1a;

  .#{$ant-prefix}-form {
    background-color: #0a0d1a;
  }

  .#{$ant-prefix}-form-item-label>label {
    color: $themeFontColor;
  }

  /*表單組件重置*/
  .#{$ant-prefix}-radio,
  .#{$ant-prefix}-radio-wrapper {
    color: $themeFontColor !important;

    .#{$ant-prefix}-radio-inner {
      background-color: transparent;
    }

    .#{$ant-prefix}-radio-checked::after {
      border: 1px solid $themeNormal;
    }

    .#{$ant-prefix}-radio-inner::after {
      background-color: #fff;
    }
  }

  .#{$ant-prefix}-radio-disabled {
    color: rgba(255, 255, 255, 0.4) !important;
  }

  .#{$ant-prefix}-radio-disabled+span {
    color: rgba(255, 255, 255, 0.4) !important;
  }

  .#{$ant-prefix}-radio-input:focus+.#{$ant-prefix}-radio-inner {
    box-shadow: 0 0 0 0 transparent;
  }

  .#{$ant-prefix}-input-outlined:hover,
  .#{$ant-prefix}-input-outlined:focus-within {
    background-color: transparent;
  }

  .#{$ant-prefix}-input-textarea,
  .#{$ant-prefix}-input-textarea-show-count {
    color: white;
    position: relative;

    .#{$ant-prefix}-input {
      background: $bgInput !important;
      border: 1px solid $borderLightGray;
      border-radius: 2px;
      color: $themeFontColor;

      &:focus {
        border-color: $borderFocus;
      }
    }

    &.#{$ant-prefix}-input-textarea-show-count::after {
      position: absolute;
      bottom: 0;
      right: 5px;
      color: $themeFontColor;
    }

    .#{$ant-prefix}-input-data-count {
      color: rgb(255 255 255 / 45%);
    }

    &>textarea::placeholder {
      color: $fontPlaceholder;
    }
  }

  .#{$ant-prefix}-form-item-has-error .#{$ant-prefix}-input:focus {
    border-color: $waring !important;
  }


  /*----- input -----*/

  .#{$ant-prefix}-input {
    background: $bgInput !important;
    border: 1px solid $borderLightGray;
    border-radius: 2px;
    color: $themeFontColor !important;

    &:focus {
      border-color: $borderFocus;
    }
  }

  .#{$ant-prefix}-select-selection-search-input {
    color: rgba(255, 255, 255, 0.8) !important;
  }

  .#{$ant-prefix}-input-affix-wrapper {
    background-color: transparent;
    border: 1px solid $borderLightGray;
    font-size: $sizeNormal;
  }

  .#{$ant-prefix}-input-affix-wrapper>input::-webkit-input-placeholder {
    color: $fontPlaceholder;
  }

  .#{$ant-prefix}-input-affix-wrapper:not(.#{$ant-prefix}-input-affix-wrapper-disabled):hover {
    border: 1px solid $themeHover !important;
  }

  .#{$ant-prefix}-form-item-has-error :not(.#{$ant-prefix}-input-affix-wrapper-disabled):not(.#{$ant-prefix}-input-affix-wrapper-borderless).#{$ant-prefix}-input-affix-wrapper {
    background-color: transparent !important;
    border: 1px solid $waring !important;
  }

  .#{$ant-prefix}-input-clear-icon {
    background: $bg;
    color: $gray;
  }

  /*modal重置*/

  .#{$ant-prefix}-modal-content {
    background: $bg !important;
    border-radius: 2px !important;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    color: #fff;
    padding: 12px !important;

    .#{$ant-prefix}-modal-close {
      color: rgba(255, 255, 255, 0.6);
    }

    .#{$ant-prefix}-modal-header {
      background: transparent;
      border-bottom: 1px solid $bgWeak;
      padding-bottom: 12px;

      .#{$ant-prefix}-modal-title {
        font-size: $sizeMedium;
        color: $themeFontColor;
      }
    }

    .#{$ant-prefix}-modal-footer {
      border-top: 1px solid $bgWeak;
      padding-top: 12px;
    }
  }

  &.#{$ant-prefix}-modal-confirm {
    .#{$ant-prefix}-modal-confirm-body {
      padding: 8px 0;
      text-align: center;

      &>.anticon {
        float: inherit;
      }

      .#{$ant-prefix}-modal-confirm-title {
        display: inline-block;
        color: $themeFontColor;
        vertical-align: text-bottom;
      }
    }

    .#{$ant-prefix}-modal-confirm-content {
      color: $themeFontColor;
    }

    .#{$ant-prefix}-modal-confirm-btns {
      float: inherit;
      text-align: center;
    }
  }

  .#{$ant-prefix}-modal-confirm-title {
    display: inline-block;
    color: $themeFontColor !important;
    vertical-align: text-bottom;
  }

  .#{$ant-prefix}-modal-confirm-content {
    color: $themeFontColor !important;
  }


  /*form表单重置*/
  .#{$ant-prefix}-form-item-label>label {
    color: $themeFontColor;
  }

  /*button重置*/

  .#{$ant-prefix}-btn {
    border-radius: 2px;
    color: $white !important;
    background: $bgDark !important;
    border: 1px solid $borderFocus !important;

    &:hover,
    &:active {
      background: transparent !important;
      border: 1px solid $themeNormal !important;
      color: $themeNormal !important;
    }

    &:focus {
      color: $white;
      border: 1px solid $borderDark;
      background: $bgDark;
    }
  }

  .#{$ant-prefix}-btn[disabled],
  .#{$ant-prefix}-btn[disabled]:hover {
    color: rgba(255, 255, 255, 0.33);
    background: none;
    border-color: rgba(255, 255, 255, 0.33);
  }

  .#{$ant-prefix}-btn-link {
    background: transparent;
    // border-color: transparent;
    color: $white;

    &:hover,
    &:active,
    &:focus {
      border: 0 solid transparent;
      color: $themeNormal;
    }
  }

  .#{$ant-prefix}-btn-primary {
    background: $themeNormal !important;
    border-color: $themeNormal !important;

    &:hover,
    &:active,
    &:focus {
      color: $white !important;
      background: $themeHover !important;
      border-color: $themeHover !important;
    }
  }

  .#{$ant-prefix}-btn-primary[disabled],
  .#{$ant-prefix}-btn-primary[disabled]:hover,
  .#{$ant-prefix}-btn-primary[disabled]:focus,
  .#{$ant-prefix}-btn-primary[disabled]:active {
    background: $themeDisabled;
    border-color: $themeDisabled;
    color: $white;
  }

  // 自定义button的样式
  .btn-customize {
    background: linear-gradient(180deg, rgba(13, 133, 255, 1) 0%, rgba(56, 80, 235, 1) 100%) !important;
    border-radius: 1px;
  }

  /*pagination*/

  .#{$ant-prefix}-pagination {
    color: $themeFontColor;

    .#{$ant-prefix}-pagination-prev .#{$ant-prefix}-pagination-item-link,
    .#{$ant-prefix}-pagination-next .#{$ant-prefix}-pagination-item-link {
      background-color: $bgDark;
      border-color: $borderDark;
      color: $themeFontColor;
      border-radius: 2px;
    }

    .#{$ant-prefix}-pagination-item {
      background-color: $bgDark;
      border-color: $borderDark;
      border-radius: 2px;

      a {
        color: $themeFontColor;
      }
    }

    .#{$ant-prefix}-pagination-item-active {
      background: $themeNormal;

      a {
        color: $white;
      }
    }

    .#{$ant-prefix}-pagination-jump-prev .#{$ant-prefix}-pagination-item-container .#{$ant-prefix}-pagination-item-ellipsis,
    .#{$ant-prefix}-pagination-jump-next .#{$ant-prefix}-pagination-item-container .#{$ant-prefix}-pagination-item-ellipsis {
      color: $themeFontColor;
    }

    .#{$ant-prefix}-pagination-options-quick-jumper input {
      background-color: $bgDark;
      border-color: $borderDark;
      color: $themeFontColor;
      border-radius: 2px;
    }

    .#{$ant-prefix}-pagination-options-quick-jumper input:focus-within {
      border-color: #1677ff;
      box-shadow: 0 0 0 2px rgba(5, 145, 255, 0.1);
      outline: 0;
      background-color: transparent;
    }

    .#{$ant-prefix}-pagination-options-quick-jumper input:focus {
      border-color: #1677ff;
      box-shadow: 0 0 0 2px rgba(5, 145, 255, 0.1);
      outline: 0;
      background-color: transparent;
    }

    .#{$ant-prefix}-pagination-options-quick-jumper input:hover {
      border-color: #1677ff;
      box-shadow: 0 0 0 2px rgba(5, 145, 255, 0.1);
      outline: 0;
      background-color: transparent;
    }

    .#{$ant-prefix}-select-single:not(.#{$ant-prefix}-select-customize-input) .#{$ant-prefix}-select-selector {
      background-color: $bgDark !important;
      border-color: $borderDark !important;
      color: $themeFontColor !important;
      border-radius: 2px !important;
    }

    .#{$ant-prefix}-select-arrow {
      color: $gray !important;
    }
  }

  .#{$ant-prefix}-form-middle {
    .#{$ant-prefix}-form-item-label>label {
      color: $themeFontColor;
      font-size: $sizeNormal;

      &::after {
        content: "";
      }
    }

    .#{$ant-prefix}-btn-middle,
    .#{$ant-prefix}-input-middle,
    .#{$ant-prefix}-select-middle,
    .#{$ant-prefix}-picker-middle .#{$ant-prefix}-picker-input>input {
      font-size: $sizeNormal;
    }

    .#{$ant-prefix}-input-middle,
    .#{$ant-prefix}-btn-middle {
      height: $height32;
      padding: 3.5px 11px;
    }

    .#{$ant-prefix}-picker-middle {
      height: $height32;
      padding: 3px 11px;
      width: 100%;
    }

    .#{$ant-prefix}-picker-middle {

      .#{$ant-prefix}-picker-suffix,
      .#{$ant-prefix}-picker-clear {
        font-size: $sizeNormal;
      }
    }
  }

  /*----- 重置large表单 -----*/

  .#{$ant-prefix}-form-large {
    .#{$ant-prefix}-form-item-label>label {
      color: $themeFontColor;
      font-size: $sizeNormal;

      &::after {
        content: "";
      }
    }

    .#{$ant-prefix}-btn-lg,
    .#{$ant-prefix}-input-lg,
    .#{$ant-prefix}-select-lg,
    .#{$ant-prefix}-picker-large .#{$ant-prefix}-picker-input>input {
      font-size: $sizeNormal;
    }

    .#{$ant-prefix}-input-lg,
    .#{$ant-prefix}-btn-lg {
      height: $height32;
      padding: 3.5px 11px;
    }

    .#{$ant-prefix}-picker-large {
      height: $height32;
      padding: 3px 11px;
      width: 100%;
    }

    .#{$ant-prefix}-picker-large {

      .#{$ant-prefix}-picker-suffix,
      .#{$ant-prefix}-picker-clear {
        font-size: $sizeNormal;
      }
    }
  }

  /*table重置*/

  .#{$ant-prefix}-table-container {
    border-top: 0 !important;
  }

  .#{$ant-prefix}-table {
    font-size: $sizeNormal;
    color: $themeFontColor;
    background: transparent;
    border: 0 !important;

    &-filter-trigger {
      font-size: $sizeNormal;
      color: $themeFontColor;

      &.active {
        color: #0098ff;
      }
    }

    &-filter-trigger-container {
      bottom: -1px;
    }

    tr.#{$ant-prefix}-table-expanded-row,
    tr.#{$ant-prefix}-table-expanded-row:hover {
      background: $bgTableHeader;
    }

    &-filter-trigger-container-open,
    &-filter-trigger-container:hover,
    &-thead th.#{$ant-prefix}-table-column-has-sorters:hover .#{$ant-prefix}-table-filter-trigger-container,
    &-thead th.#{$ant-prefix}-table-column-has-sorters:hover .#{$ant-prefix}-table-filter-trigger-container:hover {
      background: $bgTableHeader;
    }

    &-filter-trigger-container-open .#{$ant-prefix}-table-filter-trigger,
    &-filter-trigger:hover {
      color: $themeFontColor;
    }

    &-filter-dropdown-btns {
      border-top: none;
    }

    &-filter-dropdown {
      background: linear-gradient(180deg, #3b3f54 0%, #3b3f54 100%);
    }

    .#{$ant-prefix}-table-header>table {
      border-top: 0 !important;
    }

    &-thead {
      background: $bgTableHeader !important;

      tr {
        th {
          color: $themeFontColor !important;
          background: $bgTableHeader !important;
          border: 0 !important;
          padding: 13px 16px;
        }

        .#{$ant-prefix}-table-column-sort {
          background: $bgDark !important;
        }

        .#{$ant-prefix}-table-column-has-sorters:hover {
          background: $bgDark;
        }
      }
    }

    &-tbody {
      tr {
        &:nth-child(even)>td {
          background: $bgTableEven;
        }

        &:nth-child(odd)>td {
          background: $bgTableOdd;
        }

        td {
          color: $themeFontColor !important;
          padding: 13px 16px !important;
          border-bottom: 1px solid rgba(10, 13, 26, 0.4) !important;
        }

        &.#{$ant-prefix}-table-row>.#{$ant-prefix}-table-cell-row-hover {
          background: #3a3f57 !important;
        }

        td.#{$ant-prefix}-table-column-sort {
          background: $bgDark !important;
        }
      }
    }

    tr>th,
    tr>td,
    .#{$ant-prefix}-table-container,
    .#{$ant-prefix}-table-cell-fix-right-first::after {
      border-right: 0 !important;
      border-left: 0 !important;
    }

    .#{$ant-prefix}-table-cell-scrollbar {
      box-shadow: none;
    }

    tr.#{$ant-prefix}-table-measure-row {
      visibility: collapse;
    }

    tr.#{$ant-prefix}-table-expanded-row {
      .#{$ant-prefix}-table-cell {
        background: #13172e !important;
      }
    }
  }

  .#{$ant-prefix}-table-wrapper .#{$ant-prefix}-table-tbody>tr.#{$ant-prefix}-table-placeholder:hover>td {
    background: #3a3f57;
  }

  /* 自定义 Ant Design Table 滚动条样式 */
  .#{$ant-prefix}-table-body::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .#{$ant-prefix}-table-body::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 8px;
  }

  .#{$ant-prefix}-table-body::-webkit-scrollbar-thumb {
    background: $bgTableOdd;
    border-radius: 10px;
  }

  .#{$ant-prefix}-table-body::-webkit-scrollbar-thumb:hover {
    background: $bgTableOdd;
  }

  /* Firefox 支持 */
  .#{$ant-prefix}-table-body {
    scrollbar-width: thin;
    scrollbar-color: $scrollBar $bgTableOdd;
  }

  .#{$ant-prefix}-table-container table {
    background-color: $bgTableOdd;
    border-right: none;
  }

  /*----- 多选 -----*/

  .#{$ant-prefix}-checkbox {
    &-inner {
      background: none !important;
      border: 1px solid rgba(137, 139, 149, 1) !important;
    }

    &-checked {
      .#{$ant-prefix}-checkbox-inner {
        background: $themeNormal !important;
        border: 1px solid $themeNormal !important;

        &::after {
          border-color: $bgDark !important;
        }
      }

      &::after {
        border: 1px solid $themeNormal;
      }
    }

    &-wrapper {
      color: white;
    }
  }

  .#{$ant-prefix}-checkbox-indeterminate .#{$ant-prefix}-checkbox-inner {
    background-color: $bg;
    border-color: rgba(146, 150, 173, 1);
  }

  .#{$ant-prefix}-dropdown {

    &-placement-bottomCenter>.#{$ant-prefix}-dropdown-arrow,
    &-placement-bottomLeft>.#{$ant-prefix}-dropdown-arrow,
    &-placement-bottomRight>.#{$ant-prefix}-dropdown-arrow {
      border-top-color: $bgDropDown;
      border-left-color: $bgDropDown;
    }

    &-menu {
      background: linear-gradient(180deg, $bgDropDown 0%, $bgDropDown 100%);

      &-item,
      &-submenu-title {
        color: $white;

        &:hover {
          background-color: transparent;
          color: $themeNormal;

          a {
            color: $themeNormal;
          }
        }

        a {
          color: $white;
        }
      }

      &-item-selected,
      &-submenu-title-selected,
      &-item-selected>a,
      &-submenu-title-selected>a {
        background-color: transparent;
      }
    }
  }

  /*----- 重置message提示 -----*/

  .#{$ant-prefix}-message {
    margin-top: 100px;
  }

  .pickerSelect .#{$ant-prefix}-select-selector {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;

    .#{$ant-prefix}-select-selection-overflow {
      margin-top: 2px;
    }
  }

  .#{$ant-prefix}-message-notice {
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: normal;
    color: gray;

    &.#{$ant-prefix}-message-notice-success {
      .#{$ant-prefix}-message-notice-content {
        background: linear-gradient(rgba(20, 186, 183, 0.2), rgba(20, 186, 183, 0.2)),
          linear-gradient(rgba(10, 13, 26, 0.6), rgba(10, 13, 26, 0.6)) !important;
        border: 1px solid rgba(20, 186, 183, 0.2);
        border-radius: 4px;
        color: #fff !important;
      }
    }

    &.#{$ant-prefix}-message-notice-error {
      .#{$ant-prefix}-message-notice-content {
        background: linear-gradient(rgba(255, 122, 122, 0.2), rgba(255, 122, 122, 0.2)),
          linear-gradient(rgba(10, 13, 26, 0.6), rgba(10, 13, 26, 0.6)) !important;
        border: 1px solid rgba(255, 87, 77, 0.2);
        border-radius: 4px;
        color: #fff !important;
      }
    }

    &.#{$ant-prefix}-message-notice-warning {
      .#{$ant-prefix}-message-notice-content {
        background: linear-gradient(rgba(255, 158, 11, 0.2), rgba(255, 158, 11, 0.2)),
          linear-gradient(rgba(10, 13, 26, 0.6), rgba(10, 13, 26, 0.6)) !important;
        border: 1px solid rgba(255, 158, 11, 0.2);
        border-radius: 4px;
        color: #fff !important;
      }
    }
  }

  /*----- tab组件 -----*/
  .#{$ant-prefix}-tabs-tab {
    color: $themeFontColor;

    &:hover {
      color: $themeHover;
    }
  }

  .#{$ant-prefix}-tabs-ink-bar {
    color: $themeNormal;
  }

  .#{$ant-prefix}-tabs-tab.#{$ant-prefix}-tabs-tab-active .#{$ant-prefix}-tabs-tab-btn {
    color: $themeNormal;
    text-shadow: 0 0 0.25px currentColor;
  }

  .#{$ant-prefix}-empty-normal {
    color: rgba(0, 0, 0, 0.8);

    .#{$ant-prefix}-empty-description {
      color: $themeFontColor;
    }
  }

  /*-----menu组件-----*/
  .#{$ant-prefix}-menu-dark .#{$ant-prefix}-menu-inline.#{$ant-prefix}-menu-sub {
    background-color: #1b2038;
  }

  .#{$ant-prefix}-menu.#{$ant-prefix}-menu-dark,
  .#{$ant-prefix}-menu-dark .#{$ant-prefix}-menu-sub,
  .#{$ant-prefix}-menu.#{$ant-prefix}-menu-dark .#{$ant-prefix}-menu-sub {
    background-color: #1b2038;
  }

  .#{$ant-prefix}-menu-dark.#{$ant-prefix}-menu-dark:not(.#{$ant-prefix}-menu-horizontal) .#{$ant-prefix}-menu-item-selected {
    background: linear-gradient(90.29deg,
        rgba(51, 128, 254, 0.6) 0%,
        rgba(51, 128, 254, 0.6) 0%,
        rgba(13, 133, 255, 0.25) 100%,
        rgba(51, 128, 254, 0.6) 100%);
  }

  /* ant-popover*/
  .#{$ant-prefix}-popover {
    .#{$ant-prefix}-popover-arrow-content {
      background-color: $bg;
    }

    .#{$ant-prefix}-popover-inner {
      background-color: $bg;

      .#{$ant-prefix}-popover-message {
        color: $themeFontColor;
      }
    }
  }

  .#{$ant-prefix}-form-item-control-input-content {

    &>textarea::placeholder,
    &>input::placeholder {
      color: $fontPlaceholder;
    }
  }

  .#{$ant-prefix}-dropdown .#{$ant-prefix}-dropdown-menu .#{$ant-prefix}-dropdown-menu-item {
    color: #fff;
  }

}

.x-coreui-dark-theme {
  ::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 8px;
    /*高宽分别对应横竖滚动条的尺寸*/
    height: 8px;
  }

  ::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 8px;
    box-shadow: 0;
    background: rgba(255, 255, 255, .15);
  }

  ::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    box-shadow: 8px;
    border-radius: 0;
    background: transparent;
  }

  ::-webkit-scrollbar-track-piece {
    background: transparent;
  }

  .#{$ant-prefix}-table-cell-scrollbar {
    display: none;
  }

  /*----- 下拉框 -----*/
  .#{$ant-prefix}-select {
    color: #fff;

    .#{$ant-prefix}-select-selector {
      background: $bgInput !important;
      border: 1px solid $borderLightGray !important;
      border-radius: 2px !important;

      .#{$ant-prefix}-select-selection-item {
        color: $themeFontColor;
      }
    }

    &.#{$ant-prefix}-select-focused {
      box-shadow: none;

      .#{$ant-prefix}-select-selector {
        border-color: $borderFocus !important;
      }
    }

    .#{$ant-prefix}-select-arrow {
      color: $gray !important;
    }

    &.#{$ant-prefix}-select-multiple {
      .#{$ant-prefix}-select-selection-item {
        background: #1b2038 !important;
        border: none;
      }

      .#{$ant-prefix}-select-selection-item-remove svg {
        color: $gray !important;
      }
    }

    .#{$ant-prefix}-select-selection-placeholder {
      color: $fontPlaceholder;
    }
  }

  .#{$ant-prefix}-select-dropdown,
  .#{$ant-prefix}-tree-select-dropdown {
    background-color: $bg;
    border-radius: 4px 4px 4px 4px 0;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.5);
    color: #fff;

    .#{$ant-prefix}-select-item,
    .#{$ant-prefix}-select-tree,
    .#{$ant-prefix}-empty {
      color: $themeFontColor;
    }

    .#{$ant-prefix}-select-item-option-selected:not(.#{$ant-prefix}-select-item-option-disabled),
    .#{$ant-prefix}-select-item-option-active:not(.#{$ant-prefix}-select-item-option-disabled),
    .#{$ant-prefix}-select-tree-node-content-wrapper:hover,
    .#{$ant-prefix}-select-tree-node-content-wrapper-normal:hover {
      background-color: $bgDarkHover;
      color: $themeNormal;
    }

    .#{$ant-prefix}-cascader-menu-item-expand .#{$ant-prefix}-cascader-menu-item-expand-icon,
    .#{$ant-prefix}-cascader-menu-item-loading-icon {
      color: $gray;
    }

    .#{$ant-prefix}-cascader-menu {
      border-right: 1px solid $borderDarkGray;

      .#{$ant-prefix}-cascader-menu-item-selected:not(.#{$ant-prefix}-cascader-menu-item-disabled),
      .#{$ant-prefix}-cascader-menu-item-active:not(.#{$ant-prefix}-cascader-menu-item-disabled) {
        background-color: $bgDarkHover;
        color: $themeNormal;
      }

      .#{$ant-prefix}-cascader-menu-item {
        color: $themeFontColor;
        border-bottom: 1px solid rgba(0, 0, 0, 0.2);

        &:hover {
          background-color: $bgDarkHover;
          color: $themeNormal;
        }
      }
    }
  }

  .#{$ant-prefix}-select-selection-placeholder {
    color: $fontPlaceholder;
  }

  .#{$ant-prefix}-select-clear {
    background: $bg;
    color: $gray;
  }

  .#{$ant-prefix}-select-clear:hover {
    background: $bg;
    color: $gray;
  }

  .#{$ant-prefix}-select-single.#{$ant-prefix}-select-lg:not(.#{$ant-prefix}-select-customize-input) .#{$ant-prefix}-select-selector {
    height: $height32;
  }

  .#{$ant-prefix}-select-single.#{$ant-prefix}-select-lg:not(.#{$ant-prefix}-select-customize-input):not(.#{$ant-prefix}-select-customize-input) .#{$ant-prefix}-select-selection-search-input {
    height: $height32;
  }

  .#{$ant-prefix}-select-single.#{$ant-prefix}-select-lg:not(.#{$ant-prefix}-select-customize-input) .#{$ant-prefix}-select-selector::after,
  .#{$ant-prefix}-select-single.#{$ant-prefix}-select-lg:not(.#{$ant-prefix}-select-customize-input) .#{$ant-prefix}-select-selector .#{$ant-prefix}-select-selection-item,
  .#{$ant-prefix}-select-single.#{$ant-prefix}-select-lg:not(.#{$ant-prefix}-select-customize-input) .#{$ant-prefix}-select-selector .#{$ant-prefix}-select-selection-placeholder {
    line-height: $height32;
  }

  /*----- 重置日历组件 -----*/

  .#{$ant-prefix}-picker {
    background: $bgInput !important;
    color: $themeFontColor;
    border: 1px solid $borderLightGray !important;

    .#{$ant-prefix}-picker-input>input {
      color: $themeFontColor !important;
      line-height: 24px;
    }

    .#{$ant-prefix}-picker-input>input::-webkit-input-placeholder {
      color: $fontPlaceholder;
    }

    .#{$ant-prefix}-picker-input>input:-ms-input-placeholder {
      color: $fontPlaceholder;
    }

    .#{$ant-prefix}-picker-input>input::-ms-input-placeholder {
      color: $fontPlaceholder;
    }

    .#{$ant-prefix}-picker-input>input::placeholder {
      color: $fontPlaceholder;
    }

    .#{$ant-prefix}-picker-suffix {
      color: $gray !important;
    }

    .#{$ant-prefix}-picker-clear {
      background: $bg !important;
      color: $gray;
    }

    .#{$ant-prefix}-picker-separator {
      font-size: 20px;
      color: $gray !important;
    }

    &:hover,
    &:active,
    &:focus {
      border: 1px solid $themeNormal !important;
    }

    .#{$ant-prefix}-picker-range-arrow::after {
      border-color: $bg $bg transparent transparent;
    }
  }

  .#{$ant-prefix}-picker-dropdown .#{$ant-prefix}-picker-panel-container {
    background: $bg !important;
    border-radius: 4px 4px 4px 4px 0;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.5) !important;
    color: $white !important;

    .#{$ant-prefix}-picker-panel {
      border: 1px solid $borderDarkGray !important;

      .#{$ant-prefix}-picker-year-panel,
      .#{$ant-prefix}-picker-month-panel,
      .#{$ant-prefix}-picker-decade-panel {
        .#{$ant-prefix}-picker-header {
          border-bottom: 1px solid $borderDarkGray;
          color: white !important;

          .#{$ant-prefix}-picker-header-view button {
            color: $white !important;

            &:hover,
            &:active {
              color: $themeNormal !important;
            }
          }

          button {
            color: $gray;

            &:hover,
            &:active {
              color: $white;
            }
          }
        }

        .#{$ant-prefix}-picker-content {
          .#{$ant-prefix}-picker-cell {
            color: $gray !important;
          }

          th,
          .#{$ant-prefix}-picker-cell-in-view {
            color: $white !important;
          }

          .#{$ant-prefix}-picker-cell-disabled {
            &::before {
              background: transparent !important;
            }

            .#{$ant-prefix}-picker-cell-inner {
              color: $gray !important;
            }
          }

          .#{$ant-prefix}-picker-cell-in-view.#{$ant-prefix}-picker-cell-range-start:not(.#{$ant-prefix}-picker-cell-range-start-single)::before,
          .#{$ant-prefix}-picker-cell-in-view.#{$ant-prefix}-picker-cell-range-end:not(.#{$ant-prefix}-picker-cell-range-end-single)::before {
            background: $bgDark !important;
          }

          .#{$ant-prefix}-picker-cell-in-view.#{$ant-prefix}-picker-cell-in-range::before {
            background: $bgDark !important;
          }

          .#{$ant-prefix}-picker-cell-in-view.#{$ant-prefix}-picker-cell-in-range.#{$ant-prefix}-picker-cell-range-hover::before,
          .#{$ant-prefix}-picker-cell-in-view.#{$ant-prefix}-picker-cell-range-start.#{$ant-prefix}-picker-cell-range-hover::before,
          .#{$ant-prefix}-picker-cell-in-view.#{$ant-prefix}-picker-cell-range-end.#{$ant-prefix}-picker-cell-range-hover::before,
          .#{$ant-prefix}-picker-cell-in-view.#{$ant-prefix}-picker-cell-range-start:not(.#{$ant-prefix}-picker-cell-range-start-single).#{$ant-prefix}-picker-cell-range-hover-start::before,
          .#{$ant-prefix}-picker-cell-in-view.#{$ant-prefix}-picker-cell-range-end:not(.#{$ant-prefix}-picker-cell-range-end-single).#{$ant-prefix}-picker-cell-range-hover-end::before,
          .#{$ant-prefix}-picker-panel> :not(.#{$ant-prefix}-picker-date-panel) .#{$ant-prefix}-picker-cell-in-view.#{$ant-prefix}-picker-cell-in-range.#{$ant-prefix}-picker-cell-range-hover-start::before,
          .#{$ant-prefix}-picker-panel> :not(.#{$ant-prefix}-picker-date-panel) .#{$ant-prefix}-picker-cell-in-view.#{$ant-prefix}-picker-cell-in-range.#{$ant-prefix}-picker-cell-range-hover-end::before {
            background: $bgPickerRangeHover !important;
          }

          .#{$ant-prefix}-picker-cell-in-view.#{$ant-prefix}-picker-cell-in-range.#{$ant-prefix}-picker-cell-range-hover-start .#{$ant-prefix}-picker-cell-inner::after,
          .#{$ant-prefix}-picker-cell-in-view.#{$ant-prefix}-picker-cell-in-range.#{$ant-prefix}-picker-cell-range-hover-end .#{$ant-prefix}-picker-cell-inner::after {
            background: $bgPickerRangeHover !important;
          }

          .#{$ant-prefix}-picker-focused {
            box-shadow: unset;
          }

          .#{$ant-prefix}-picker-cell:hover:not(.#{$ant-prefix}-picker-cell-in-view) .#{$ant-prefix}-picker-cell-inner,
          .#{$ant-prefix}-picker-cell:hover:not(.#{$ant-prefix}-picker-cell-selected):not(.#{$ant-prefix}-picker-cell-range-start):not(.#{$ant-prefix}-picker-cell-range-end):not(.#{$ant-prefix}-picker-cell-range-hover-start):not(.#{$ant-prefix}-picker-cell-range-hover-end) .#{$ant-prefix}-picker-cell-inner {
            background: #1c1f37 !important;
          }
        }
      }

      .#{$ant-prefix}-picker-datetime-panel {
        .#{$ant-prefix}-picker-date-panel {
          .#{$ant-prefix}-picker-header {
            border-bottom: 1px solid $borderDarkGray;

            .#{$ant-prefix}-picker-header-view button {
              color: $white !important;

              &:hover,
              &:active {
                color: $themeNormal !important;
              }
            }

            button {
              color: $gray;

              &:hover,
              &:active {
                color: $white;
              }
            }
          }

          .#{$ant-prefix}-picker-content {
            .#{$ant-prefix}-picker-cell {
              color: $gray !important;
            }

            th,
            .#{$ant-prefix}-picker-cell-in-view {
              color: $white !important;
            }

            .#{$ant-prefix}-picker-cell-disabled {
              &::before {
                background: transparent !important;
              }

              .#{$ant-prefix}-picker-cell-inner {
                color: $gray !important;
              }
            }

            .#{$ant-prefix}-picker-cell-in-view.#{$ant-prefix}-picker-cell-range-start:not(.#{$ant-prefix}-picker-cell-range-start-single)::before,
            .#{$ant-prefix}-picker-cell-in-view.#{$ant-prefix}-picker-cell-range-end:not(.#{$ant-prefix}-picker-cell-range-end-single)::before {
              background: $bgDark !important;
            }

            .#{$ant-prefix}-picker-cell-in-view.#{$ant-prefix}-picker-cell-in-range::before {
              background: $bgDark !important;
            }

            .#{$ant-prefix}-picker-cell-in-view.#{$ant-prefix}-picker-cell-in-range.#{$ant-prefix}-picker-cell-range-hover::before,
            .#{$ant-prefix}-picker-cell-in-view.#{$ant-prefix}-picker-cell-range-start.#{$ant-prefix}-picker-cell-range-hover::before,
            .#{$ant-prefix}-picker-cell-in-view.#{$ant-prefix}-picker-cell-range-end.#{$ant-prefix}-picker-cell-range-hover::before,
            .#{$ant-prefix}-picker-cell-in-view.#{$ant-prefix}-picker-cell-range-start:not(.#{$ant-prefix}-picker-cell-range-start-single).#{$ant-prefix}-picker-cell-range-hover-start::before,
            .#{$ant-prefix}-picker-cell-in-view.#{$ant-prefix}-picker-cell-range-end:not(.#{$ant-prefix}-picker-cell-range-end-single).#{$ant-prefix}-picker-cell-range-hover-end::before,
            .#{$ant-prefix}-picker-panel> :not(.#{$ant-prefix}-picker-date-panel) .#{$ant-prefix}-picker-cell-in-view.#{$ant-prefix}-picker-cell-in-range.#{$ant-prefix}-picker-cell-range-hover-start::before,
            .#{$ant-prefix}-picker-panel> :not(.#{$ant-prefix}-picker-date-panel) .#{$ant-prefix}-picker-cell-in-view.#{$ant-prefix}-picker-cell-in-range.#{$ant-prefix}-picker-cell-range-hover-end::before {
              background: $bgPickerRangeHover !important;
            }

            .#{$ant-prefix}-picker-cell-in-view.#{$ant-prefix}-picker-cell-in-range.#{$ant-prefix}-picker-cell-range-hover-start .#{$ant-prefix}-picker-cell-inner::after,
            .#{$ant-prefix}-picker-cell-in-view.#{$ant-prefix}-picker-cell-in-range.#{$ant-prefix}-picker-cell-range-hover-end .#{$ant-prefix}-picker-cell-inner::after {
              background: $bgPickerRangeHover !important;
            }

            .#{$ant-prefix}-picker-focused {
              box-shadow: unset;
            }

            .#{$ant-prefix}-picker-cell:hover:not(.#{$ant-prefix}-picker-cell-in-view) .#{$ant-prefix}-picker-cell-inner,
            .#{$ant-prefix}-picker-cell:hover:not(.#{$ant-prefix}-picker-cell-selected):not(.#{$ant-prefix}-picker-cell-range-start):not(.#{$ant-prefix}-picker-cell-range-end):not(.#{$ant-prefix}-picker-cell-range-hover-start):not(.#{$ant-prefix}-picker-cell-range-hover-end) .#{$ant-prefix}-picker-cell-inner {
              background: $bgWeak !important;
            }
          }
        }

        .#{$ant-prefix}-picker-time-panel {
          border-left: 1px solid $borderDarkGray;

          .#{$ant-prefix}-picker-header {
            color: $white;
            border-bottom: 1px solid $borderDarkGray;
          }

          .#{$ant-prefix}-picker-time-panel-column>li.#{$ant-prefix}-picker-time-panel-cell .#{$ant-prefix}-picker-time-panel-cell-inner {
            color: $white;

            &:hover,
            &:active,
            &:focus {
              background: $bgLightGray;
              color: $themeNormal;
            }
          }

          .#{$ant-prefix}-picker-time-panel-column>li.#{$ant-prefix}-picker-time-panel-cell-selected .#{$ant-prefix}-picker-time-panel-cell-inner {
            background: $bgLightGray;
            color: $white;
          }

          .#{$ant-prefix}-picker-time-panel-column:not(:first-child) {
            border-left: 1px solid $borderDarkGray;
          }

          .#{$ant-prefix}-picker-time-panel-cell-disabled {
            &::before {
              background: transparent !important;
            }

            .#{$ant-prefix}-picker-time-panel-cell-inner {
              color: $gray !important;
            }
          }
        }
      }

      .#{$ant-prefix}-picker-footer {
        border-top: 1px solid $borderDarkGray;
      }
    }
  }
}