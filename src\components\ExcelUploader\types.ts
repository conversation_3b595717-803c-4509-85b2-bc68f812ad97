// types.ts
import type { UploadFile } from "antd/es/upload/interface";
/** excel列校验配置 */
export interface ColumnConfig {
  key: string;
  title: string;
  required?: boolean;
  type?: "string" | "number" | "date" | "enum";
  options?: string[];
  validator?: string;
  transform?: (value: any) => any;
}

/** 校验结果 */
export interface ValidationResult {
  valid: boolean;
  errors: string[];
  isEmpty?: boolean;
}

/** 解析结果 */
export interface ParseResult {
  headers: string[];
  data: any[];
}

/** 校验函数 */
export interface ValidatorFunction {
  (value: any, config: ColumnConfig): ValidationResult;
}

/** 上传模式类型 */
export type UploadModeType = "btn" | "icon" | "dragger";

/** excel上传器属性 */
export interface ExcelUploaderProps {
  downLoadTemplate: any;
  columnConfig?: ColumnConfig[];
  onSuccess?: (data: any[]) => void;
  onError?: (error: Error) => void;
  customValidators?: Record<string, ValidatorFunction>;
  // 新增弹窗相关属性
  visible: boolean;
  onCancel: () => void;
  title?: string;
  description?: React.ReactNode;
  postProcessor?: (validData: any[]) => Promise<PostProcessResult>;
  uploadToS3?: boolean;
  /** 上传模式：btn-按钮 | icon-图标 | dragger-拖拽 */
  uploadMode?: UploadModeType;
  showStepGuidance?: boolean;
  downloadIconText?: string;
  // S3 上传相关配置（当 uploadToS3 为 true 时需要）
  s3Config?: {
    /** 获取预签名地址 */
    getPreSignatureUrl: string;
    /** 桶名称 */
    bucketName?: string;
    /** 服务域 */
    LOPDN: string;
    /** 最大文件大小 */
    maxFileSize?: number;
    /** 文件大小单位 */
    unit?: "MB" | "GB" | "KB";
    /** md5校验 */
    needMd5Validete?: boolean;
    /** S3 上传成功回调 */
    onS3Success?: (fileKey: string) => void;
    /** S3 上传失败回调 */
    onS3Error?: (error: Error, body?: Object) => void;
    /** S3 删除回调 */
    onS3Delete?: (fileKey: string) => void;
  };
  // 拖拽模式配置
  draggerConfig?: {
    title?: string;
    hint?: string;
  };
  onS3UploadConfirm?: (fileList: UploadFile[]) => void;
}

/** 导入结果 */
export interface ImportResult {
  totalCount: number; // 总数据条数
  successCount: number; // 成功条数
  errorCount: number; // 失败条数
}

export interface ImportErrorModalProps {
  visible: boolean;
  onClose: () => void;
  importResult: ImportResult;
  onDownload: () => void;
}

export interface PostProcessResult<T = any> {
  validData: T[]; // 最终有效数据
  invalidData: T[]; // 不符合业务规则的数据
  errors?: string[]; // 错误信息
}
