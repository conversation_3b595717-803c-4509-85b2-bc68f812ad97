import { Meta, Story, Canvas } from '@storybook/blocks';
import * as CommonTableStories from './CommonTable.stories';

<Meta title="CommonTable/ConfigurableColumns" />

# 可配置列功能

CommonTable 组件现在支持列的显示/隐藏配置、列固定和列排序，类似于 Ant Design ProComponents 的 ProTable。

## 基本用法

可配置列功能允许用户通过界面控制表格中哪些列应该显示或隐藏、列的固定位置（左侧/右侧/不固定）以及列的排序。这对于包含大量列的表格特别有用，用户可以根据自己的需求自定义表格视图。

<Canvas of={CommonTableStories.ConfigurableColumnsLight} />

## 暗色主题

可配置列功能在暗色主题下同样可用：

<Canvas of={CommonTableStories.ConfigurableColumnsDark} />

## 如何使用

要在你的 CommonTable 中启用可配置列功能，需要添加以下属性：

```tsx
import React, { useState } from 'react';
import { CommonTable, createDefaultColumnsState, sortColumnsByState } from '@/components';

const MyComponent = () => {
  const columns = [
    // 你的列定义
    { title: '序号', dataIndex: 'index', key: 'index' },
    { title: '姓名', dataIndex: 'name', key: 'name' },
    { title: '年龄', dataIndex: 'age', key: 'age' },
    { title: '地址', dataIndex: 'address', key: 'address' },
    { title: '电话', dataIndex: 'phone', key: 'phone' },
    // 更多列...
  ];
  
  // 默认隐藏的列
  const defaultHiddenColumns = ['address', 'phone'];
  
  // 默认固定在左侧的列
  const defaultLeftFixedColumns = ['index'];
  
  // 默认固定在右侧的列
  const defaultRightFixedColumns = [];
  
  // 列显示状态
  const [columnsState, setColumnsState] = useState(
    createDefaultColumnsState(columns, defaultHiddenColumns, defaultLeftFixedColumns, defaultRightFixedColumns)
  );
  
  // 应用列排序
  const sortedColumns = sortColumnsByState(columns, columnsState);
  
  return (
    <CommonTable
      // 其他 CommonTable 属性...
      columns={sortedColumns} // 使用排序后的列
      rowKey="id"
      tableKey="my-unique-table-key" // 用于持久化存储
      showColumnSetting={true} // 显示列设置按钮
      columnsState={{
        value: columnsState,
        onChange: setColumnsState,
        persistenceType: 'localStorage', // 或 'sessionStorage'
      }}
      defaultColumnsState={createDefaultColumnsState(columns, defaultHiddenColumns, defaultLeftFixedColumns, defaultRightFixedColumns)}
      // 列设置组件属性
      columnSettingProps={{
        draggable: true, // 是否支持拖拽排序
        checkable: true, // 是否显示复选框
        showListItemOption: true, // 是否显示列操作选项
      }}
    />
  );
};
```

## 属性说明

### CommonTable 组件属性

| 属性 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| showColumnSetting | 是否显示列设置按钮 | boolean | false |
| columnsState | 列显示状态配置 | object | - |
| columnsState.value | 列显示状态 | Record<string, ColumnState> | - |
| columnsState.onChange | 列显示状态变化回调 | (map: Record<string, ColumnState>) => void | - |
| columnsState.persistenceType | 持久化类型 | 'localStorage' \| 'sessionStorage' | - |
| defaultColumnsState | 默认列设置 | Record<string, ColumnState> | - |
| tableKey | 表格唯一标识，用于持久化存储列设置 | string | - |
| columnSettingProps | 列设置组件属性 | object | - |

### ColumnState 类型

| 属性 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| show | 是否显示列 | boolean | true |
| fixed | 列的固定位置 | 'left' \| 'right' \| undefined | undefined |
| order | 列的排序位置 | number | - |
| disable | 是否禁用列设置 | boolean | false |

### columnSettingProps 属性

| 属性 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| checkedReset | 是否显示重置按钮 | boolean | true |
| checkable | 是否显示复选框 | boolean | true |
| draggable | 是否支持拖拽排序 | boolean | true |
| showListItemOption | 是否显示列操作选项 | boolean | true |
| listsHeight | 列表的高度 | number | 280 |

## 工具函数

### createDefaultColumnsState

创建默认的列显示状态配置。

```tsx
import { createDefaultColumnsState } from '@/components';

// 创建默认列设置，隐藏 'phone' 和 'email' 列
// 并将 'index' 列固定在左侧，'action' 列固定在右侧
const defaultState = createDefaultColumnsState(
  columns, 
  ['phone', 'email'], // 隐藏的列
  ['index'],          // 左侧固定列
  ['action']          // 右侧固定列
);
```

### filterColumnsByState

根据列显示状态过滤列并应用固定列设置。

```tsx
import { filterColumnsByState } from '@/components';

// 根据列显示状态过滤列并应用固定列设置
const visibleColumns = filterColumnsByState(columns, columnsState);
```

### sortColumnsByState

根据列状态对列进行排序。

```tsx
import { sortColumnsByState } from '@/components';

// 根据列状态对列进行排序
const sortedColumns = sortColumnsByState(columns, columnsState);
```
